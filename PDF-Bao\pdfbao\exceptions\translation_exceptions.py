"""
翻译相关异常
"""

from typing import Optional, Dict, Any
from .base_exceptions import pdfbaoError, RecoverableError, CriticalError, ErrorSeverity


class TranslationError(pdfbaoError):
    """翻译基础错误"""
    
    def __init__(
        self,
        message: str,
        text: Optional[str] = None,
        lang_in: Optional[str] = None,
        lang_out: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.text = text
        self.lang_in = lang_in
        self.lang_out = lang_out
        
        if text:
            self.add_context('text_length', len(text))
            self.add_context('text_preview', text[:100] + '...' if len(text) > 100 else text)
        if lang_in:
            self.add_context('source_language', lang_in)
        if lang_out:
            self.add_context('target_language', lang_out)


class TranslatorInitError(CriticalError):
    """翻译器初始化错误"""
    
    def __init__(
        self,
        translator_type: str,
        message: Optional[str] = None,
        **kwargs
    ):
        message = message or f"Failed to initialize {translator_type} translator"
        super().__init__(message, **kwargs)
        self.translator_type = translator_type
        self.add_context('translator_type', translator_type)
        self.add_suggestion("Check API credentials and network connection")
        self.add_suggestion("Verify translator configuration")


class TranslationTimeoutError(RecoverableError):
    """翻译超时错误"""
    
    def __init__(
        self,
        timeout_seconds: float,
        text_length: Optional[int] = None,
        **kwargs
    ):
        message = f"Translation timed out after {timeout_seconds} seconds"
        if text_length:
            message += f" (text length: {text_length} characters)"
        
        super().__init__(
            message,
            recovery_action="Retry with longer timeout or split text",
            max_retries=3,
            **kwargs
        )
        self.timeout_seconds = timeout_seconds
        self.text_length = text_length
        self.add_context('timeout_seconds', timeout_seconds)
        if text_length:
            self.add_context('text_length', text_length)
        
        self.add_suggestion("Increase timeout setting")
        self.add_suggestion("Split long text into smaller chunks")


class TranslationRateLimitError(RecoverableError):
    """翻译速率限制错误"""
    
    def __init__(
        self,
        retry_after: Optional[float] = None,
        current_qps: Optional[int] = None,
        **kwargs
    ):
        message = "Translation rate limit exceeded"
        if retry_after:
            message += f", retry after {retry_after} seconds"
        
        super().__init__(
            message,
            recovery_action="Wait and retry with lower QPS",
            max_retries=5,
            **kwargs
        )
        self.retry_after = retry_after
        self.current_qps = current_qps
        
        if retry_after:
            self.add_context('retry_after_seconds', retry_after)
        if current_qps:
            self.add_context('current_qps', current_qps)
        
        self.add_suggestion("Reduce QPS setting")
        self.add_suggestion("Wait before retrying")
        if current_qps and current_qps > 1:
            self.add_suggestion(f"Try reducing QPS from {current_qps} to {max(1, current_qps // 2)}")


class TranslationAPIError(RecoverableError):
    """翻译API错误"""
    
    def __init__(
        self,
        api_error_code: Optional[str] = None,
        api_error_message: Optional[str] = None,
        status_code: Optional[int] = None,
        **kwargs
    ):
        message = "Translation API error"
        if api_error_code:
            message += f" [{api_error_code}]"
        if api_error_message:
            message += f": {api_error_message}"
        
        # 根据状态码确定是否可恢复
        max_retries = 3
        if status_code:
            if status_code >= 500:  # 服务器错误，可重试
                max_retries = 5
            elif status_code in [401, 403]:  # 认证错误，不可重试
                max_retries = 0
        
        super().__init__(
            message,
            recovery_action="Check API credentials and retry",
            max_retries=max_retries,
            **kwargs
        )
        
        self.api_error_code = api_error_code
        self.api_error_message = api_error_message
        self.status_code = status_code
        
        if api_error_code:
            self.add_context('api_error_code', api_error_code)
        if status_code:
            self.add_context('http_status_code', status_code)
        
        # 根据错误类型提供建议
        if status_code == 401:
            self.add_suggestion("Check API key validity")
        elif status_code == 403:
            self.add_suggestion("Check API permissions and quota")
        elif status_code == 429:
            self.add_suggestion("Reduce request rate")
        elif status_code and status_code >= 500:
            self.add_suggestion("API service may be temporarily unavailable, retry later")


class TranslationCacheError(pdfbaoError):
    """翻译缓存错误"""
    
    def __init__(
        self,
        operation: str,
        cache_key: Optional[str] = None,
        **kwargs
    ):
        message = f"Translation cache error during {operation}"
        super().__init__(message, severity=ErrorSeverity.LOW, **kwargs)
        self.operation = operation
        self.cache_key = cache_key
        
        self.add_context('cache_operation', operation)
        if cache_key:
            self.add_context('cache_key', cache_key)
        
        self.add_suggestion("Clear translation cache")
        self.add_suggestion("Disable cache temporarily")


class TranslationQualityError(pdfbaoError):
    """翻译质量错误"""
    
    def __init__(
        self,
        issue_type: str,
        original_text: Optional[str] = None,
        translated_text: Optional[str] = None,
        **kwargs
    ):
        message = f"Translation quality issue: {issue_type}"
        super().__init__(message, severity=ErrorSeverity.MEDIUM, **kwargs)
        self.issue_type = issue_type
        self.original_text = original_text
        self.translated_text = translated_text
        
        self.add_context('quality_issue', issue_type)
        if original_text:
            self.add_context('original_length', len(original_text))
        if translated_text:
            self.add_context('translated_length', len(translated_text))
        
        self.add_suggestion("Review translation manually")
        self.add_suggestion("Try different translation model")
        self.add_suggestion("Adjust translation prompt")


class GlossaryError(pdfbaoError):
    """术语表错误"""
    
    def __init__(
        self,
        glossary_name: str,
        operation: str,
        **kwargs
    ):
        message = f"Glossary error in '{glossary_name}' during {operation}"
        super().__init__(message, **kwargs)
        self.glossary_name = glossary_name
        self.operation = operation
        
        self.add_context('glossary_name', glossary_name)
        self.add_context('operation', operation)
        
        self.add_suggestion("Check glossary file format")
        self.add_suggestion("Validate glossary entries")


class ContextExtractionError(pdfbaoError):
    """上下文提取错误"""
    
    def __init__(
        self,
        context_type: str,
        page_number: Optional[int] = None,
        **kwargs
    ):
        message = f"Failed to extract {context_type} context"
        if page_number:
            message += f" from page {page_number}"
        
        super().__init__(message, **kwargs)
        self.context_type = context_type
        self.page_number = page_number
        
        self.add_context('context_type', context_type)
        if page_number:
            self.add_context('page_number', page_number)
        
        self.add_suggestion("Check document structure")
        self.add_suggestion("Try different layout analysis settings")
