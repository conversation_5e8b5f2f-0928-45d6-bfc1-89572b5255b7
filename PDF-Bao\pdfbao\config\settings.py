"""
pdfbao 配置设置类
使用 Pydantic 进行配置验证和类型检查
"""

from pathlib import Path
from typing import Optional, List, Dict, Any, Union
from enum import Enum
import os

try:
    from pydantic import BaseModel, Field, validator, root_validator
except ImportError:
    # 如果没有安装 pydantic，使用简化版本
    class BaseModel:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
    
    def Field(default=None, **kwargs):
        return default
    
    def validator(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def root_validator(*args, **kwargs):
        def decorator(func):
            return func
        return decorator


class WatermarkMode(str, Enum):
    """水印模式枚举"""
    WATERMARKED = "watermarked"
    NO_WATERMARK = "no_watermark"
    BOTH = "both"


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class APISettings(BaseModel):
    """API 相关设置"""
    base_url: str = Field(default="", description="API 基础 URL")
    api_key: str = Field(default="", description="API 密钥")
    model: str = Field(default="gpt-3.5-turbo", description="使用的模型")
    timeout: int = Field(default=30, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, description="最大重试次数")
    
    @validator('base_url')
    def validate_base_url(cls, v):
        if v and not (v.startswith('http://') or v.startswith('https://')):
            raise ValueError('base_url must start with http:// or https://')
        return v
    
    @validator('timeout')
    def validate_timeout(cls, v):
        if v <= 0:
            raise ValueError('timeout must be positive')
        return v


class PerformanceSettings(BaseModel):
    """性能相关设置"""
    qps: int = Field(default=1, description="每秒查询数")
    pool_max_workers: Optional[int] = Field(default=None, description="线程池最大工作线程数")
    max_memory_mb: int = Field(default=1024, description="最大内存使用量（MB）")
    batch_size: int = Field(default=10, description="批处理大小")
    
    @validator('qps')
    def validate_qps(cls, v):
        if v <= 0:
            raise ValueError('qps must be positive')
        return v
    
    @validator('pool_max_workers')
    def validate_pool_max_workers(cls, v, values):
        if v is None:
            return values.get('qps', 1)
        if v <= 0:
            raise ValueError('pool_max_workers must be positive')
        return v


class TranslationSettings(BaseModel):
    """翻译相关设置"""
    lang_in: str = Field(default="en", description="源语言")
    lang_out: str = Field(default="zh", description="目标语言")
    pages: Optional[str] = Field(default=None, description="要翻译的页面范围")
    min_text_length: int = Field(default=5, description="最小文本长度")
    skip_clean: bool = Field(default=False, description="跳过清理")
    enhance_compatibility: bool = Field(default=False, description="增强兼容性")
    disable_rich_text_translate: bool = Field(default=False, description="禁用富文本翻译")
    auto_extract_glossary: bool = Field(default=True, description="自动提取术语表")
    split_short_lines: bool = Field(default=False, description="强制分割短行")
    short_line_split_factor: float = Field(default=0.8, description="分割因子")
    
    @validator('min_text_length')
    def validate_min_text_length(cls, v):
        if v < 0:
            raise ValueError('min_text_length must be non-negative')
        return v

    @validator('short_line_split_factor')
    def validate_short_line_split_factor(cls, v):
        if v <= 0 or v > 1:
            raise ValueError('short_line_split_factor must be between 0 and 1')
        return v


class OutputSettings(BaseModel):
    """输出相关设置"""
    output_dir: Optional[Path] = Field(default=None, description="输出目录")
    no_dual: bool = Field(default=False, description="不生成双语版本")
    no_mono: bool = Field(default=False, description="不生成单语版本")
    watermark_mode: WatermarkMode = Field(default=WatermarkMode.WATERMARKED, description="水印模式")
    save_auto_extracted_glossary: bool = Field(default=True, description="保存自动提取的术语表")
    
    @validator('output_dir')
    def validate_output_dir(cls, v):
        if v is not None:
            v = Path(v)
            if not v.exists():
                try:
                    v.mkdir(parents=True, exist_ok=True)
                except OSError as e:
                    raise ValueError(f'Cannot create output directory: {e}')
        return v


class UISettings(BaseModel):
    """UI 相关设置"""
    theme: str = Field(default="dark", description="界面主题")
    window_width: int = Field(default=1200, description="窗口宽度")
    window_height: int = Field(default=800, description="窗口高度")
    auto_save_config: bool = Field(default=True, description="自动保存配置")
    show_debug_info: bool = Field(default=False, description="显示调试信息")


class pdfbaoSettings(BaseModel):
    """pdfbao 主配置类"""
    api: APISettings = Field(default_factory=APISettings)
    performance: PerformanceSettings = Field(default_factory=PerformanceSettings)
    translation: TranslationSettings = Field(default_factory=TranslationSettings)
    output: OutputSettings = Field(default_factory=OutputSettings)
    ui: UISettings = Field(default_factory=UISettings)
    
    # 全局设置
    debug: bool = Field(default=False, description="调试模式")
    log_level: LogLevel = Field(default=LogLevel.INFO, description="日志级别")
    working_dir: Optional[Path] = Field(default=None, description="工作目录")
    
    @root_validator
    def validate_settings(cls, values):
        """全局配置验证"""
        # 如果启用调试模式，自动设置日志级别
        if values.get('debug', False):
            values['log_level'] = LogLevel.DEBUG
        
        # 设置默认工作目录
        if values.get('working_dir') is None:
            values['working_dir'] = Path.home() / '.cache' / 'pdfbao'
        
        return values
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self.dict()
    
    def update_from_dict(self, data: Dict[str, Any]) -> 'pdfbaoSettings':
        """从字典更新配置"""
        for key, value in data.items():
            if hasattr(self, key):
                if isinstance(getattr(self, key), BaseModel):
                    # 嵌套配置对象
                    nested_obj = getattr(self, key)
                    if isinstance(value, dict):
                        for nested_key, nested_value in value.items():
                            if hasattr(nested_obj, nested_key):
                                setattr(nested_obj, nested_key, nested_value)
                else:
                    setattr(self, key, value)
        return self
