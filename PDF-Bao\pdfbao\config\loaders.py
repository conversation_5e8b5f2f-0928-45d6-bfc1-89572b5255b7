"""
配置加载器
支持从多种源加载配置：环境变量、文件、命令行参数等
"""

import os
import json
import toml
from pathlib import Path
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
import logging

logger = logging.getLogger(__name__)


class ConfigLoader(ABC):
    """配置加载器基类"""
    
    @abstractmethod
    def load(self) -> Dict[str, Any]:
        """加载配置"""
        pass
    
    @abstractmethod
    def can_load(self) -> bool:
        """检查是否可以加载配置"""
        pass


class EnvConfigLoader(ConfigLoader):
    """环境变量配置加载器"""
    
    def __init__(self, prefix: str = "PDF_BAO_"):
        self.prefix = prefix
    
    def can_load(self) -> bool:
        """检查是否有相关环境变量"""
        return any(key.startswith(self.prefix) for key in os.environ)
    
    def load(self) -> Dict[str, Any]:
        """从环境变量加载配置"""
        config = {}
        
        # 环境变量映射
        env_mapping = {
            f"{self.prefix}API_BASE_URL": ("api", "base_url"),
            f"{self.prefix}API_KEY": ("api", "api_key"),
            f"{self.prefix}MODEL": ("api", "model"),
            f"{self.prefix}QPS": ("performance", "qps"),
            f"{self.prefix}MAX_WORKERS": ("performance", "pool_max_workers"),
            f"{self.prefix}LANG_IN": ("translation", "lang_in"),
            f"{self.prefix}LANG_OUT": ("translation", "lang_out"),
            f"{self.prefix}SPLIT_SHORT_LINES": ("translation", "split_short_lines"),
            f"{self.prefix}SHORT_LINE_SPLIT_FACTOR": ("translation", "short_line_split_factor"),
            f"{self.prefix}DEBUG": ("debug",),
            f"{self.prefix}LOG_LEVEL": ("log_level",),
        }
        
        for env_key, config_path in env_mapping.items():
            value = os.getenv(env_key)
            if value is not None:
                # 类型转换
                if env_key.endswith(("_QPS", "_MAX_WORKERS")):
                    try:
                        value = int(value)
                    except ValueError:
                        logger.warning(f"Invalid integer value for {env_key}: {value}")
                        continue
                elif env_key.endswith(("_DEBUG", "_SPLIT_SHORT_LINES")):
                    value = value.lower() in ("true", "1", "yes", "on")
                elif env_key.endswith("_SHORT_LINE_SPLIT_FACTOR"):
                    try:
                        value = float(value)
                    except ValueError:
                        logger.warning(f"Invalid float value for {env_key}: {value}")
                        continue
                
                # 设置嵌套配置
                self._set_nested_config(config, config_path, value)
        
        return config
    
    def _set_nested_config(self, config: Dict[str, Any], path: tuple, value: Any):
        """设置嵌套配置值"""
        current = config
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[path[-1]] = value


class FileConfigLoader(ConfigLoader):
    """文件配置加载器"""
    
    def __init__(self, file_path: Path):
        self.file_path = Path(file_path)
    
    def can_load(self) -> bool:
        """检查文件是否存在且可读"""
        return self.file_path.exists() and self.file_path.is_file()
    
    def load(self) -> Dict[str, Any]:
        """从文件加载配置"""
        if not self.can_load():
            return {}
        
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                if self.file_path.suffix.lower() == '.json':
                    return json.load(f)
                elif self.file_path.suffix.lower() in ('.toml', '.tml'):
                    return toml.load(f)
                elif self.file_path.suffix.lower() in ('.env',):
                    return self._load_env_file(f)
                else:
                    # 尝试按行解析
                    return self._load_key_value_file(f)
        except Exception as e:
            logger.error(f"Failed to load config from {self.file_path}: {e}")
            return {}
    
    def _load_env_file(self, file_handle) -> Dict[str, Any]:
        """加载 .env 格式文件"""
        config = {}
        for line in file_handle:
            line = line.strip()
            if line and not line.startswith('#'):
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # 映射到配置结构
                    if key == 'base_url':
                        config.setdefault('api', {})['base_url'] = value
                    elif key == 'api':
                        config.setdefault('api', {})['api_key'] = value
                    elif key == 'model':
                        config.setdefault('api', {})['model'] = value
                    else:
                        config[key] = value
        return config
    
    def _load_key_value_file(self, file_handle) -> Dict[str, Any]:
        """加载键值对格式文件"""
        config = {}
        for line in file_handle:
            line = line.strip()
            if line and not line.startswith('#'):
                if '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
        return config


class CLIConfigLoader(ConfigLoader):
    """命令行参数配置加载器"""
    
    def __init__(self, args: Optional[Dict[str, Any]] = None):
        self.args = args or {}
    
    def can_load(self) -> bool:
        """检查是否有命令行参数"""
        return bool(self.args)
    
    def load(self) -> Dict[str, Any]:
        """从命令行参数加载配置"""
        config = {}
        
        # 参数映射
        arg_mapping = {
            'base_url': ('api', 'base_url'),
            'api_key': ('api', 'api_key'),
            'model': ('api', 'model'),
            'qps': ('performance', 'qps'),
            'pool_max_workers': ('performance', 'pool_max_workers'),
            'lang_in': ('translation', 'lang_in'),
            'lang_out': ('translation', 'lang_out'),
            'pages': ('translation', 'pages'),
            'split_short_lines': ('translation', 'split_short_lines'),
            'short_line_split_factor': ('translation', 'short_line_split_factor'),
            'output_dir': ('output', 'output_dir'),
            'no_dual': ('output', 'no_dual'),
            'no_mono': ('output', 'no_mono'),
            'debug': ('debug',),
        }
        
        for arg_key, config_path in arg_mapping.items():
            if arg_key in self.args and self.args[arg_key] is not None:
                value = self.args[arg_key]
                self._set_nested_config(config, config_path, value)
        
        return config
    
    def _set_nested_config(self, config: Dict[str, Any], path: tuple, value: Any):
        """设置嵌套配置值"""
        current = config
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[path[-1]] = value


class MultiSourceConfigLoader:
    """多源配置加载器"""
    
    def __init__(self, loaders: List[ConfigLoader]):
        self.loaders = loaders
    
    def load(self) -> Dict[str, Any]:
        """按优先级顺序加载配置"""
        merged_config = {}
        
        for loader in self.loaders:
            if loader.can_load():
                try:
                    config = loader.load()
                    merged_config = self._deep_merge(merged_config, config)
                    logger.debug(f"Loaded config from {loader.__class__.__name__}")
                except Exception as e:
                    logger.error(f"Failed to load config from {loader.__class__.__name__}: {e}")
        
        return merged_config
    
    def _deep_merge(self, base: Dict[str, Any], update: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并字典"""
        result = base.copy()
        
        for key, value in update.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
