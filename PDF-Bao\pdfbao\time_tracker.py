"""
时间统计模块 - 线程安全的翻译过程时间跟踪器
重构版本：使用无锁设计和异步更新机制，避免死锁问题
"""

import time
import threading
import queue
import logging
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class TimeRecord:
    """不可变的时间记录 - 线程安全"""
    name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    
    @property
    def is_running(self) -> bool:
        return self.end_time is None
    
    @property
    def current_duration(self) -> float:
        if self.is_running:
            return time.time() - self.start_time
        return self.duration or 0.0
    
    def finish(self) -> 'TimeRecord':
        """返回一个已完成的新记录"""
        if not self.is_running:
            return self
        end_time = time.time()
        return TimeRecord(
            name=self.name,
            start_time=self.start_time,
            end_time=end_time,
            duration=end_time - self.start_time
        )
    
    def get_formatted_duration(self) -> str:
        """获取格式化的持续时间字符串"""
        duration = self.current_duration
        if duration < 1:
            return f"{duration*1000:.1f}ms"
        elif duration < 60:
            return f"{duration:.2f}s"
        elif duration < 3600:
            minutes = int(duration // 60)
            seconds = duration % 60
            return f"{minutes}m {seconds:.1f}s"
        else:
            hours = int(duration // 3600)
            minutes = int((duration % 3600) // 60)
            seconds = duration % 60
            return f"{hours}h {minutes}m {seconds:.1f}s"


@dataclass(frozen=True)
class TimeSnapshot:
    """时间统计快照 - 不可变数据结构，线程安全"""
    overall_record: Optional[TimeRecord] = None
    stage_records: Dict[str, TimeRecord] = field(default_factory=dict)
    file_records: Dict[str, TimeRecord] = field(default_factory=dict)
    stage_history: List[Tuple[str, float]] = field(default_factory=list)
    current_stage: Optional[str] = None
    total_files: int = 0
    completed_files: int = 0
    timestamp: float = field(default_factory=time.time)


class SafeTranslationTimeTracker:
    """线程安全的翻译时间跟踪器
    
    设计原则：
    1. 使用不可变数据结构避免竞争条件
    2. 异步事件通知机制
    3. 故障隔离 - 错误不影响翻译流程
    4. 简化的API接口
    """
    
    def __init__(self):
        self._lock = threading.RLock()  # 使用可重入锁
        self._overall_record: Optional[TimeRecord] = None
        self._stage_records: Dict[str, TimeRecord] = {}
        self._file_records: Dict[str, TimeRecord] = {}
        self._stage_history: List[Tuple[str, float]] = []
        self._current_stage: Optional[str] = None
        self._total_files = 0
        self._completed_files = 0
        
        # 事件通知系统
        self._event_queue = queue.Queue()
        self._listeners: List[Callable[[str, Any], None]] = []
        self._enabled = True
        
    def enable(self):
        """启用时间统计"""
        with self._lock:
            self._enabled = True
            
    def disable(self):
        """禁用时间统计"""
        with self._lock:
            self._enabled = False
            
    def is_enabled(self) -> bool:
        """检查是否启用"""
        return self._enabled
        
    def _safe_execute(self, operation: Callable, *args, **kwargs) -> bool:
        """安全执行操作，错误时返回False但不抛出异常"""
        if not self._enabled:
            return False
            
        try:
            operation(*args, **kwargs)
            return True
        except Exception as e:
            logger.warning(f"时间统计操作失败: {e}")
            return False
            
    def _notify_listeners(self, event_type: str, data: Any):
        """通知监听器（异步）"""
        try:
            self._event_queue.put_nowait((event_type, data))
        except queue.Full:
            logger.warning("时间统计事件队列已满，跳过事件通知")
            
    def add_listener(self, listener: Callable[[str, Any], None]):
        """添加事件监听器"""
        with self._lock:
            self._listeners.append(listener)
            
    def start_overall(self) -> bool:
        """开始整体计时"""
        def _start():
            with self._lock:
                self._overall_record = TimeRecord("整体翻译", time.time())
                self._notify_listeners("overall_started", self._overall_record)
                
        return self._safe_execute(_start)
        
    def end_overall(self) -> bool:
        """结束整体计时"""
        def _end():
            with self._lock:
                if self._overall_record and self._overall_record.is_running:
                    self._overall_record = self._overall_record.finish()
                    self._notify_listeners("overall_ended", self._overall_record)
                    
        return self._safe_execute(_end)
        
    def start_stage(self, stage_name: str) -> bool:
        """开始阶段计时"""
        def _start():
            with self._lock:
                # 结束当前阶段
                if self._current_stage and self._current_stage in self._stage_records:
                    self._end_stage_internal(self._current_stage)
                    
                # 开始新阶段
                self._stage_records[stage_name] = TimeRecord(stage_name, time.time())
                self._current_stage = stage_name
                self._notify_listeners("stage_started", {
                    "stage_name": stage_name,
                    "record": self._stage_records[stage_name]
                })
                
        return self._safe_execute(_start)
        
    def end_stage(self, stage_name: str) -> bool:
        """结束阶段计时"""
        def _end():
            with self._lock:
                self._end_stage_internal(stage_name)
                
        return self._safe_execute(_end)
        
    def _end_stage_internal(self, stage_name: str):
        """内部阶段结束方法（需要在锁内调用）"""
        if stage_name in self._stage_records:
            record = self._stage_records[stage_name]
            if record.is_running:
                finished_record = record.finish()
                self._stage_records[stage_name] = finished_record
                self._stage_history.append((stage_name, finished_record.duration))
                self._notify_listeners("stage_ended", {
                    "stage_name": stage_name,
                    "record": finished_record
                })
                if self._current_stage == stage_name:
                    self._current_stage = None
                    
    def start_file(self, file_path: str) -> bool:
        """开始文件处理计时"""
        def _start():
            with self._lock:
                self._file_records[file_path] = TimeRecord(file_path, time.time())
                self._notify_listeners("file_started", {
                    "file_path": file_path,
                    "record": self._file_records[file_path]
                })
                
        return self._safe_execute(_start)
        
    def end_file(self, file_path: str) -> bool:
        """结束文件处理计时"""
        def _end():
            with self._lock:
                if file_path in self._file_records:
                    record = self._file_records[file_path]
                    if record.is_running:
                        finished_record = record.finish()
                        self._file_records[file_path] = finished_record
                        self._completed_files += 1
                        self._notify_listeners("file_ended", {
                            "file_path": file_path,
                            "record": finished_record
                        })
                        
        return self._safe_execute(_end)
        
    def set_total_files(self, total: int) -> bool:
        """设置总文件数"""
        def _set():
            with self._lock:
                self._total_files = total
                self._completed_files = 0
                
        return self._safe_execute(_set)
        
    def reset(self) -> bool:
        """重置所有计时器"""
        def _reset():
            with self._lock:
                self._overall_record = None
                self._stage_records.clear()
                self._file_records.clear()
                self._stage_history.clear()
                self._current_stage = None
                self._total_files = 0
                self._completed_files = 0
                self._notify_listeners("reset", None)
                
        return self._safe_execute(_reset)
        
    def get_snapshot(self) -> Optional[TimeSnapshot]:
        """获取当前时间统计快照"""
        try:
            with self._lock:
                return TimeSnapshot(
                    overall_record=self._overall_record,
                    stage_records=self._stage_records.copy(),
                    file_records=self._file_records.copy(),
                    stage_history=self._stage_history.copy(),
                    current_stage=self._current_stage,
                    total_files=self._total_files,
                    completed_files=self._completed_files
                )
        except Exception as e:
            logger.warning(f"获取时间统计快照失败: {e}")
            return None


# 兼容性包装器，提供旧API接口
class CompatibilityWrapper:
    """为旧代码提供兼容性的包装器"""

    def __init__(self, tracker: SafeTranslationTimeTracker):
        self._tracker = tracker

    def start_overall(self):
        return self._tracker.start_overall()

    def end_overall(self):
        return self._tracker.end_overall()

    def start_stage(self, stage_name: str):
        return self._tracker.start_stage(stage_name)

    def end_stage(self, stage_name: str):
        return self._tracker.end_stage(stage_name)

    def start_file(self, file_path: str):
        return self._tracker.start_file(file_path)

    def end_file(self, file_path: str):
        return self._tracker.end_file(file_path)

    def set_total_files(self, total: int):
        return self._tracker.set_total_files(total)

    def reset(self):
        return self._tracker.reset()

    def get_current_stage_info(self) -> Optional[Tuple[str, float]]:
        """获取当前阶段信息"""
        snapshot = self._tracker.get_snapshot()
        if snapshot and snapshot.current_stage and snapshot.current_stage in snapshot.stage_records:
            record = snapshot.stage_records[snapshot.current_stage]
            return (snapshot.current_stage, record.current_duration)
        return None

    def get_stage_summary(self) -> Dict[str, Dict[str, Any]]:
        """获取阶段统计摘要"""
        snapshot = self._tracker.get_snapshot()
        if not snapshot:
            return {}

        summary = {}
        for stage_name, record in snapshot.stage_records.items():
            summary[stage_name] = {
                'name': stage_name,
                'duration': record.current_duration,
                'formatted_duration': record.get_formatted_duration(),
                'formatted': record.get_formatted_duration(),  # 向后兼容
                'is_running': record.is_running
            }
        return summary

    def get_file_summary(self) -> Dict[str, Dict[str, Any]]:
        """获取文件处理统计摘要"""
        snapshot = self._tracker.get_snapshot()
        if not snapshot:
            return {}

        summary = {}
        for file_path, record in snapshot.file_records.items():
            summary[file_path] = {
                'name': file_path,
                'duration': record.current_duration,
                'formatted_duration': record.get_formatted_duration(),
                'formatted': record.get_formatted_duration(),  # 向后兼容
                'is_running': record.is_running
            }
        return summary

    def get_overall_summary(self) -> Dict[str, Any]:
        """获取整体统计摘要"""
        snapshot = self._tracker.get_snapshot()
        if not snapshot:
            return {}

        overall_record = snapshot.overall_record
        if overall_record:
            return {
                'duration': overall_record.current_duration,
                'formatted_duration': overall_record.get_formatted_duration(),
                'is_running': overall_record.is_running,
                'total_files': snapshot.total_files,
                'completed_files': snapshot.completed_files,
                'progress': snapshot.completed_files / max(1, snapshot.total_files) * 100
            }
        return {}

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        snapshot = self._tracker.get_snapshot()
        if not snapshot:
            return {}

        stage_summary = self.get_stage_summary()
        file_summary = self.get_file_summary()
        overall_summary = self.get_overall_summary()

        # 计算平均文件处理时间
        completed_file_times = [info['duration'] for info in file_summary.values() if not info['is_running']]
        avg_file_time = sum(completed_file_times) / len(completed_file_times) if completed_file_times else 0

        # 找出最耗时的阶段
        slowest_stage = None
        max_duration = 0
        for stage_name, info in stage_summary.items():
            if info['duration'] > max_duration:
                max_duration = info['duration']
                slowest_stage = stage_name

        # 格式化平均时间
        if avg_file_time > 0:
            avg_record = TimeRecord("avg", time.time() - avg_file_time, time.time(), avg_file_time)
            avg_file_time_formatted = avg_record.get_formatted_duration()
        else:
            avg_file_time_formatted = "0s"

        return {
            'overall': overall_summary,
            'stages': stage_summary,
            'files': file_summary,
            'avg_file_time': avg_file_time,
            'avg_file_time_formatted': avg_file_time_formatted,
            'slowest_stage': slowest_stage,
            'slowest_stage_time': max_duration
        }


# 全局实例 - 使用新的安全实现
safe_translation_time_tracker = SafeTranslationTimeTracker()

# 向后兼容的别名
translation_time_tracker = CompatibilityWrapper(safe_translation_time_tracker)
