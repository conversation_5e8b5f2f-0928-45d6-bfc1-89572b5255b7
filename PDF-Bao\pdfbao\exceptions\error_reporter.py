"""
错误报告器
收集、格式化和报告错误信息
"""

import json
import logging
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum

from .base_exceptions import pdfbaoError, ErrorSeverity

logger = logging.getLogger(__name__)


class ReportFormat(Enum):
    """报告格式"""
    JSON = "json"
    TEXT = "text"
    HTML = "html"
    MARKDOWN = "markdown"


@dataclass
class ErrorReport:
    """错误报告"""
    error_id: str
    timestamp: datetime
    error_type: str
    error_message: str
    severity: ErrorSeverity
    component: str
    operation: str
    context: Dict[str, Any]
    suggestions: List[str]
    traceback: Optional[str] = None
    user_info: Optional[Dict[str, Any]] = None
    system_info: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['severity'] = self.severity.value
        return data


class ErrorReporter:
    """错误报告器"""
    
    def __init__(self, report_dir: Optional[Path] = None):
        self.report_dir = report_dir or Path.home() / '.pdfbao' / 'error_reports'
        self.report_dir.mkdir(parents=True, exist_ok=True)
        self.reports: List[ErrorReport] = []
        self.auto_save = True
    
    def report_error(
        self,
        error: Exception,
        component: str = "unknown",
        operation: str = "unknown",
        context: Optional[Dict[str, Any]] = None,
        user_info: Optional[Dict[str, Any]] = None
    ) -> ErrorReport:
        """报告错误"""
        error_id = self._generate_error_id()
        
        # 提取错误信息
        if isinstance(error, pdfbaoError):
            error_type = error.__class__.__name__
            error_message = error.message
            severity = error.severity
            suggestions = error.suggestions
            error_context = error.context
        else:
            error_type = error.__class__.__name__
            error_message = str(error)
            severity = ErrorSeverity.MEDIUM
            suggestions = []
            error_context = {}
        
        # 合并上下文
        if context:
            error_context.update(context)
        
        # 获取系统信息
        system_info = self._get_system_info()
        
        # 创建报告
        report = ErrorReport(
            error_id=error_id,
            timestamp=datetime.now(),
            error_type=error_type,
            error_message=error_message,
            severity=severity,
            component=component,
            operation=operation,
            context=error_context,
            suggestions=suggestions,
            traceback=traceback.format_exc(),
            user_info=user_info,
            system_info=system_info
        )
        
        # 添加到报告列表
        self.reports.append(report)
        
        # 自动保存
        if self.auto_save:
            self._save_report(report)
        
        logger.info(f"Error reported: {error_id}")
        return report
    
    def get_reports(
        self,
        severity: Optional[ErrorSeverity] = None,
        component: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[ErrorReport]:
        """获取错误报告"""
        filtered_reports = self.reports
        
        # 按严重程度过滤
        if severity:
            filtered_reports = [r for r in filtered_reports if r.severity == severity]
        
        # 按组件过滤
        if component:
            filtered_reports = [r for r in filtered_reports if r.component == component]
        
        # 按时间排序（最新的在前）
        filtered_reports.sort(key=lambda r: r.timestamp, reverse=True)
        
        # 限制数量
        if limit:
            filtered_reports = filtered_reports[:limit]
        
        return filtered_reports
    
    def export_reports(
        self,
        format: ReportFormat = ReportFormat.JSON,
        output_file: Optional[Path] = None,
        include_traceback: bool = False
    ) -> str:
        """导出错误报告"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = self.report_dir / f"error_report_{timestamp}.{format.value}"
        
        if format == ReportFormat.JSON:
            content = self._export_json(include_traceback)
        elif format == ReportFormat.TEXT:
            content = self._export_text(include_traceback)
        elif format == ReportFormat.HTML:
            content = self._export_html(include_traceback)
        elif format == ReportFormat.MARKDOWN:
            content = self._export_markdown(include_traceback)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"Error reports exported to: {output_file}")
        return str(output_file)
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        if not self.reports:
            return {"total_errors": 0}
        
        # 统计信息
        total_errors = len(self.reports)
        severity_counts = {}
        component_counts = {}
        error_type_counts = {}
        
        for report in self.reports:
            # 严重程度统计
            severity = report.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # 组件统计
            component_counts[report.component] = component_counts.get(report.component, 0) + 1
            
            # 错误类型统计
            error_type_counts[report.error_type] = error_type_counts.get(report.error_type, 0) + 1
        
        # 最近的错误
        recent_reports = sorted(self.reports, key=lambda r: r.timestamp, reverse=True)[:5]
        recent_errors = [
            {
                "id": r.error_id,
                "type": r.error_type,
                "message": r.error_message,
                "timestamp": r.timestamp.isoformat(),
                "severity": r.severity.value
            }
            for r in recent_reports
        ]
        
        return {
            "total_errors": total_errors,
            "severity_distribution": severity_counts,
            "component_distribution": component_counts,
            "error_type_distribution": error_type_counts,
            "recent_errors": recent_errors,
            "report_period": {
                "start": min(r.timestamp for r in self.reports).isoformat() if self.reports else None,
                "end": max(r.timestamp for r in self.reports).isoformat() if self.reports else None
            }
        }
    
    def clear_reports(self, older_than_days: Optional[int] = None):
        """清理错误报告"""
        if older_than_days is None:
            self.reports.clear()
            logger.info("All error reports cleared")
        else:
            cutoff_time = datetime.now().timestamp() - (older_than_days * 24 * 3600)
            original_count = len(self.reports)
            self.reports = [
                r for r in self.reports 
                if r.timestamp.timestamp() > cutoff_time
            ]
            cleared_count = original_count - len(self.reports)
            logger.info(f"Cleared {cleared_count} error reports older than {older_than_days} days")
    
    def _generate_error_id(self) -> str:
        """生成错误ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        import uuid
        short_uuid = str(uuid.uuid4())[:8]
        return f"ERR_{timestamp}_{short_uuid}"
    
    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        import platform
        import sys
        
        return {
            "platform": platform.platform(),
            "python_version": sys.version,
            "pdfbao_version": "0.4.15",  # 从常量获取
            "timestamp": datetime.now().isoformat()
        }
    
    def _save_report(self, report: ErrorReport):
        """保存单个报告"""
        try:
            report_file = self.report_dir / f"{report.error_id}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report.to_dict(), f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save error report: {e}")
    
    def _export_json(self, include_traceback: bool) -> str:
        """导出JSON格式"""
        data = {
            "summary": self.get_error_summary(),
            "reports": []
        }
        
        for report in self.reports:
            report_data = report.to_dict()
            if not include_traceback:
                report_data.pop('traceback', None)
            data["reports"].append(report_data)
        
        return json.dumps(data, indent=2, ensure_ascii=False)
    
    def _export_text(self, include_traceback: bool) -> str:
        """导出文本格式"""
        lines = ["pdfbao Error Report", "=" * 50, ""]
        
        # 摘要
        summary = self.get_error_summary()
        lines.extend([
            f"Total Errors: {summary['total_errors']}",
            f"Report Period: {summary.get('report_period', {}).get('start', 'N/A')} - {summary.get('report_period', {}).get('end', 'N/A')}",
            ""
        ])
        
        # 详细报告
        for i, report in enumerate(self.reports, 1):
            lines.extend([
                f"Error #{i}: {report.error_id}",
                "-" * 30,
                f"Type: {report.error_type}",
                f"Message: {report.error_message}",
                f"Severity: {report.severity.value}",
                f"Component: {report.component}",
                f"Operation: {report.operation}",
                f"Timestamp: {report.timestamp.isoformat()}",
                ""
            ])
            
            if report.suggestions:
                lines.extend(["Suggestions:"] + [f"  - {s}" for s in report.suggestions] + [""])
            
            if include_traceback and report.traceback:
                lines.extend(["Traceback:", report.traceback, ""])
        
        return "\n".join(lines)
    
    def _export_markdown(self, include_traceback: bool) -> str:
        """导出Markdown格式"""
        lines = ["# pdfbao Error Report", ""]
        
        # 摘要
        summary = self.get_error_summary()
        lines.extend([
            "## Summary",
            f"- **Total Errors:** {summary['total_errors']}",
            f"- **Report Period:** {summary.get('report_period', {}).get('start', 'N/A')} - {summary.get('report_period', {}).get('end', 'N/A')}",
            ""
        ])
        
        # 详细报告
        lines.append("## Error Details")
        for i, report in enumerate(self.reports, 1):
            lines.extend([
                f"### Error #{i}: {report.error_id}",
                f"- **Type:** {report.error_type}",
                f"- **Message:** {report.error_message}",
                f"- **Severity:** {report.severity.value}",
                f"- **Component:** {report.component}",
                f"- **Operation:** {report.operation}",
                f"- **Timestamp:** {report.timestamp.isoformat()}",
                ""
            ])
            
            if report.suggestions:
                lines.extend(["**Suggestions:**"] + [f"- {s}" for s in report.suggestions] + [""])
            
            if include_traceback and report.traceback:
                lines.extend(["**Traceback:**", "```", report.traceback, "```", ""])
        
        return "\n".join(lines)
    
    def _export_html(self, include_traceback: bool) -> str:
        """导出HTML格式"""
        # 简化的HTML导出
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>pdfbao Error Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .error { border: 1px solid #ccc; margin: 10px 0; padding: 10px; }
                .critical { border-color: #ff0000; }
                .high { border-color: #ff8800; }
                .medium { border-color: #ffaa00; }
                .low { border-color: #00aa00; }
                pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
            </style>
        </head>
        <body>
            <h1>pdfbao Error Report</h1>
        """
        
        for report in self.reports:
            severity_class = report.severity.value
            html += f"""
            <div class="error {severity_class}">
                <h3>{report.error_type}: {report.error_id}</h3>
                <p><strong>Message:</strong> {report.error_message}</p>
                <p><strong>Severity:</strong> {report.severity.value}</p>
                <p><strong>Component:</strong> {report.component}</p>
                <p><strong>Operation:</strong> {report.operation}</p>
                <p><strong>Timestamp:</strong> {report.timestamp.isoformat()}</p>
            """
            
            if report.suggestions:
                html += "<p><strong>Suggestions:</strong></p><ul>"
                for suggestion in report.suggestions:
                    html += f"<li>{suggestion}</li>"
                html += "</ul>"
            
            if include_traceback and report.traceback:
                html += f"<p><strong>Traceback:</strong></p><pre>{report.traceback}</pre>"
            
            html += "</div>"
        
        html += "</body></html>"
        return html


# 全局错误报告器实例
_error_reporter = ErrorReporter()


def get_error_reporter() -> ErrorReporter:
    """获取全局错误报告器"""
    return _error_reporter
