"""
配置管理器
统一管理 pdfbao 的所有配置
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple

from .settings import pdfbaoSettings
from .validators import ConfigValidator, ValidationError
from .loaders import (
    ConfigLoader,
    EnvConfigLoader,
    FileConfigLoader,
    CLIConfigLoader,
    MultiSourceConfigLoader
)

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[Path] = None):
        self.config_file = config_file or self._get_default_config_file()
        self.settings: Optional[pdfbaoSettings] = None
        self.validator = ConfigValidator()
        self._loaded_sources: List[str] = []
    
    def load_config(self, cli_args: Optional[Dict[str, Any]] = None) -> pdfbaoSettings:
        """
        加载配置，按优先级顺序：
        1. 命令行参数 (最高优先级)
        2. 环境变量
        3. 配置文件
        4. 默认值 (最低优先级)
        """
        loaders = []
        
        # 1. 命令行参数 (最高优先级)
        if cli_args:
            loaders.append(CLIConfigLoader(cli_args))
        
        # 2. 环境变量
        loaders.append(EnvConfigLoader())
        
        # 3. 用户配置文件
        user_config_file = Path.home() / '.pdfbao' / 'config.json'
        if user_config_file.exists():
            loaders.append(FileConfigLoader(user_config_file))
        
        # 4. 项目配置文件
        if self.config_file and self.config_file.exists():
            loaders.append(FileConfigLoader(self.config_file))
        
        # 5. .env 文件
        env_file = Path('.env')
        if env_file.exists():
            loaders.append(FileConfigLoader(env_file))
        
        # 加载配置
        multi_loader = MultiSourceConfigLoader(loaders)
        config_data = multi_loader.load()
        
        # 记录加载源
        self._loaded_sources = [
            loader.__class__.__name__ for loader in loaders if loader.can_load()
        ]
        
        # 验证配置
        is_valid, errors = self.validator.validate(config_data)
        if not is_valid:
            self._handle_validation_errors(errors)
        
        # 创建设置对象
        try:
            self.settings = pdfbaoSettings(**config_data)
        except Exception as e:
            logger.error(f"Failed to create settings object: {e}")
            # 使用默认设置
            self.settings = pdfbaoSettings()
        
        logger.info(f"Configuration loaded from: {', '.join(self._loaded_sources)}")
        return self.settings
    
    def save_config(self, config_file: Optional[Path] = None) -> bool:
        """保存配置到文件"""
        if not self.settings:
            logger.error("No settings to save")
            return False
        
        save_file = config_file or self.config_file
        if not save_file:
            save_file = Path.home() / '.pdfbao' / 'config.json'
        
        try:
            # 确保目录存在
            save_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存配置
            with open(save_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings.to_dict(), f, indent=2, ensure_ascii=False)
            
            logger.info(f"Configuration saved to: {save_file}")
            return True
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False
    
    def get_settings(self) -> pdfbaoSettings:
        """获取当前设置"""
        if not self.settings:
            return self.load_config()
        return self.settings
    
    def update_settings(self, updates: Dict[str, Any]) -> bool:
        """更新设置"""
        if not self.settings:
            self.load_config()
        
        try:
            # 验证更新
            is_valid, errors = self.validator.validate(updates)
            if not is_valid:
                self._handle_validation_errors(errors)
                return False
            
            # 应用更新
            self.settings.update_from_dict(updates)
            logger.info("Settings updated successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to update settings: {e}")
            return False
    
    def reset_to_defaults(self) -> pdfbaoSettings:
        """重置为默认配置"""
        self.settings = pdfbaoSettings()
        logger.info("Settings reset to defaults")
        return self.settings
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        if not self.settings:
            return {}
        
        return {
            'loaded_sources': self._loaded_sources,
            'api_configured': bool(self.settings.api.api_key),
            'output_dir': str(self.settings.output.output_dir) if self.settings.output.output_dir else None,
            'debug_mode': self.settings.debug,
            'language_pair': f"{self.settings.translation.lang_in} -> {self.settings.translation.lang_out}",
            'performance': {
                'qps': self.settings.performance.qps,
                'workers': self.settings.performance.pool_max_workers
            }
        }
    
    def validate_current_config(self) -> Tuple[bool, List[ValidationError]]:
        """验证当前配置"""
        if not self.settings:
            return False, [ValidationError('config', 'No configuration loaded')]
        
        return self.validator.validate(self.settings.to_dict())
    
    def _get_default_config_file(self) -> Path:
        """获取默认配置文件路径"""
        # 优先查找项目根目录的配置文件
        possible_files = [
            Path('pdfbao.toml'),
            Path('config.json'),
            Path('.pdfbao.json'),
        ]
        
        for file_path in possible_files:
            if file_path.exists():
                return file_path
        
        # 返回默认路径
        return Path.home() / '.pdfbao' / 'config.json'
    
    def _handle_validation_errors(self, errors: List[ValidationError]):
        """处理验证错误"""
        for error in errors:
            logger.error(f"Configuration validation error: {error}")
        
        # 如果有严重错误，可以选择抛出异常或使用默认值
        critical_fields = ['api.base_url', 'api.api_key']
        critical_errors = [e for e in errors if e.field in critical_fields]
        
        if critical_errors:
            logger.warning("Critical configuration errors detected, some features may not work")


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def get_settings() -> pdfbaoSettings:
    """获取当前设置的便捷函数"""
    return get_config_manager().get_settings()


def load_config(cli_args: Optional[Dict[str, Any]] = None) -> pdfbaoSettings:
    """加载配置的便捷函数"""
    return get_config_manager().load_config(cli_args)


def save_config() -> bool:
    """保存配置的便捷函数"""
    return get_config_manager().save_config()
