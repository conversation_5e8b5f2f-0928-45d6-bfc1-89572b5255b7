"""
配置相关异常
"""

from typing import Optional, List, Any
from pathlib import Path
from .base_exceptions import pdfbaoError, CriticalError, ValidationError, ErrorSeverity


class ConfigurationError(pdfbaoError):
    """配置基础错误"""
    
    def __init__(
        self,
        message: str,
        config_section: Optional[str] = None,
        config_key: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.config_section = config_section
        self.config_key = config_key
        
        if config_section:
            self.add_context('config_section', config_section)
        if config_key:
            self.add_context('config_key', config_key)


class ConfigValidationError(ValidationError):
    """配置验证错误"""
    
    def __init__(
        self,
        field: str,
        message: str,
        value: Any = None,
        expected_type: Optional[str] = None,
        valid_values: Optional[List[Any]] = None,
        **kwargs
    ):
        super().__init__(field, message, value, **kwargs)
        self.expected_type = expected_type
        self.valid_values = valid_values
        
        if expected_type:
            self.add_context('expected_type', expected_type)
        if valid_values:
            self.add_context('valid_values', valid_values)
            self.add_suggestion(f"Use one of: {', '.join(map(str, valid_values))}")


class ConfigLoadError(ConfigurationError):
    """配置加载错误"""
    
    def __init__(
        self,
        config_source: str,
        file_path: Optional[Path] = None,
        parse_error: Optional[str] = None,
        **kwargs
    ):
        message = f"Failed to load configuration from {config_source}"
        if parse_error:
            message += f": {parse_error}"
        
        super().__init__(message, **kwargs)
        self.config_source = config_source
        self.file_path = file_path
        self.parse_error = parse_error
        
        self.add_context('config_source', config_source)
        if file_path:
            self.add_context('file_path', str(file_path))
        if parse_error:
            self.add_context('parse_error', parse_error)
        
        self.add_suggestion("Check configuration file syntax")
        self.add_suggestion("Verify file permissions")
        if file_path and not file_path.exists():
            self.add_suggestion("Create configuration file")


class ConfigSaveError(ConfigurationError):
    """配置保存错误"""
    
    def __init__(
        self,
        file_path: Path,
        save_error: Optional[str] = None,
        **kwargs
    ):
        message = f"Failed to save configuration to {file_path}"
        if save_error:
            message += f": {save_error}"
        
        super().__init__(message, **kwargs)
        self.file_path = file_path
        self.save_error = save_error
        
        self.add_context('file_path', str(file_path))
        if save_error:
            self.add_context('save_error', save_error)
        
        self.add_suggestion("Check directory write permissions")
        self.add_suggestion("Ensure sufficient disk space")
        self.add_suggestion("Check if file is locked by another process")


class MissingConfigError(CriticalError):
    """缺少必需配置错误"""
    
    def __init__(
        self,
        required_config: str,
        config_section: Optional[str] = None,
        **kwargs
    ):
        message = f"Missing required configuration: {required_config}"
        if config_section:
            message += f" in section '{config_section}'"
        
        super().__init__(message, **kwargs)
        self.required_config = required_config
        self.config_section = config_section
        
        self.add_context('required_config', required_config)
        if config_section:
            self.add_context('config_section', config_section)
        
        self.add_suggestion(f"Set {required_config} in configuration")
        self.add_suggestion("Check configuration documentation")


class InvalidConfigValueError(ConfigValidationError):
    """无效配置值错误"""
    
    def __init__(
        self,
        config_key: str,
        value: Any,
        reason: str,
        **kwargs
    ):
        message = f"Invalid value for {config_key}: {reason}"
        super().__init__(config_key, message, value, **kwargs)
        self.reason = reason
        
        self.add_context('reason', reason)


class ConfigConflictError(ConfigurationError):
    """配置冲突错误"""
    
    def __init__(
        self,
        conflicting_configs: List[str],
        conflict_reason: str,
        **kwargs
    ):
        configs_str = ", ".join(conflicting_configs)
        message = f"Configuration conflict between {configs_str}: {conflict_reason}"
        
        super().__init__(message, severity=ErrorSeverity.HIGH, **kwargs)
        self.conflicting_configs = conflicting_configs
        self.conflict_reason = conflict_reason
        
        self.add_context('conflicting_configs', conflicting_configs)
        self.add_context('conflict_reason', conflict_reason)
        
        self.add_suggestion("Review conflicting configuration values")
        self.add_suggestion("Choose one configuration option")


class DeprecatedConfigError(pdfbaoError):
    """已弃用配置错误"""
    
    def __init__(
        self,
        deprecated_config: str,
        replacement: Optional[str] = None,
        removal_version: Optional[str] = None,
        **kwargs
    ):
        message = f"Configuration '{deprecated_config}' is deprecated"
        if replacement:
            message += f", use '{replacement}' instead"
        if removal_version:
            message += f" (will be removed in version {removal_version})"
        
        super().__init__(message, severity=ErrorSeverity.LOW, **kwargs)
        self.deprecated_config = deprecated_config
        self.replacement = replacement
        self.removal_version = removal_version
        
        self.add_context('deprecated_config', deprecated_config)
        if replacement:
            self.add_context('replacement', replacement)
            self.add_suggestion(f"Replace '{deprecated_config}' with '{replacement}'")
        if removal_version:
            self.add_context('removal_version', removal_version)


class ConfigMigrationError(ConfigurationError):
    """配置迁移错误"""
    
    def __init__(
        self,
        from_version: str,
        to_version: str,
        migration_issue: str,
        **kwargs
    ):
        message = f"Failed to migrate configuration from v{from_version} to v{to_version}: {migration_issue}"
        super().__init__(message, **kwargs)
        self.from_version = from_version
        self.to_version = to_version
        self.migration_issue = migration_issue
        
        self.add_context('from_version', from_version)
        self.add_context('to_version', to_version)
        self.add_context('migration_issue', migration_issue)
        
        self.add_suggestion("Backup current configuration")
        self.add_suggestion("Reset to default configuration")
        self.add_suggestion("Manual configuration update")


class EnvironmentConfigError(ConfigurationError):
    """环境配置错误"""
    
    def __init__(
        self,
        env_var: str,
        env_issue: str,
        **kwargs
    ):
        message = f"Environment variable '{env_var}' error: {env_issue}"
        super().__init__(message, **kwargs)
        self.env_var = env_var
        self.env_issue = env_issue
        
        self.add_context('environment_variable', env_var)
        self.add_context('issue', env_issue)
        
        self.add_suggestion(f"Set environment variable {env_var}")
        self.add_suggestion("Check environment variable format")
        self.add_suggestion("Use configuration file instead")
