"""
网络相关异常
"""

from typing import Optional, Dict, Any
from .base_exceptions import pdfbaoError, RecoverableError, CriticalError, ErrorSeverity


class NetworkError(RecoverableError):
    """网络基础错误"""
    
    def __init__(
        self,
        message: str,
        url: Optional[str] = None,
        status_code: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, max_retries=3, **kwargs)
        self.url = url
        self.status_code = status_code
        
        if url:
            self.add_context('url', url)
        if status_code:
            self.add_context('status_code', status_code)


class ConnectionError(NetworkError):
    """连接错误"""
    
    def __init__(
        self,
        url: str,
        connection_issue: Optional[str] = None,
        **kwargs
    ):
        message = f"Failed to connect to {url}"
        if connection_issue:
            message += f": {connection_issue}"
        
        super().__init__(
            message,
            url=url,
            recovery_action="Check network connection and retry",
            **kwargs
        )
        self.connection_issue = connection_issue
        
        if connection_issue:
            self.add_context('connection_issue', connection_issue)
        
        self.add_suggestion("Check internet connection")
        self.add_suggestion("Verify URL is correct")
        self.add_suggestion("Check firewall settings")
        self.add_suggestion("Try using VPN if blocked")


class TimeoutError(NetworkError):
    """网络超时错误"""
    
    def __init__(
        self,
        url: str,
        timeout_seconds: float,
        operation: Optional[str] = None,
        **kwargs
    ):
        message = f"Request to {url} timed out after {timeout_seconds} seconds"
        if operation:
            message += f" during {operation}"
        
        super().__init__(
            message,
            url=url,
            recovery_action="Retry with longer timeout",
            **kwargs
        )
        self.timeout_seconds = timeout_seconds
        self.operation = operation
        
        self.add_context('timeout_seconds', timeout_seconds)
        if operation:
            self.add_context('operation', operation)
        
        self.add_suggestion("Increase timeout setting")
        self.add_suggestion("Check network speed")
        self.add_suggestion("Try during off-peak hours")


class RateLimitError(NetworkError):
    """速率限制错误"""
    
    def __init__(
        self,
        url: str,
        retry_after: Optional[float] = None,
        limit_type: Optional[str] = None,
        **kwargs
    ):
        message = f"Rate limit exceeded for {url}"
        if limit_type:
            message += f" ({limit_type})"
        if retry_after:
            message += f", retry after {retry_after} seconds"
        
        super().__init__(
            message,
            url=url,
            status_code=429,
            recovery_action="Wait and retry with lower rate",
            max_retries=5,
            **kwargs
        )
        self.retry_after = retry_after
        self.limit_type = limit_type
        
        if retry_after:
            self.add_context('retry_after_seconds', retry_after)
        if limit_type:
            self.add_context('limit_type', limit_type)
        
        self.add_suggestion("Reduce request rate")
        self.add_suggestion("Wait before retrying")
        if retry_after:
            self.add_suggestion(f"Wait at least {retry_after} seconds")


class AuthenticationError(CriticalError):
    """认证错误"""
    
    def __init__(
        self,
        url: str,
        auth_issue: Optional[str] = None,
        **kwargs
    ):
        message = f"Authentication failed for {url}"
        if auth_issue:
            message += f": {auth_issue}"
        
        super().__init__(message, **kwargs)
        self.url = url
        self.auth_issue = auth_issue
        
        self.add_context('url', url)
        if auth_issue:
            self.add_context('auth_issue', auth_issue)
        
        self.add_suggestion("Check API key validity")
        self.add_suggestion("Verify authentication credentials")
        self.add_suggestion("Check account status and permissions")


class APIQuotaExceededError(CriticalError):
    """API配额超限错误"""
    
    def __init__(
        self,
        service: str,
        quota_type: str,
        reset_time: Optional[str] = None,
        **kwargs
    ):
        message = f"{service} {quota_type} quota exceeded"
        if reset_time:
            message += f", resets at {reset_time}"
        
        super().__init__(message, **kwargs)
        self.service = service
        self.quota_type = quota_type
        self.reset_time = reset_time
        
        self.add_context('service', service)
        self.add_context('quota_type', quota_type)
        if reset_time:
            self.add_context('reset_time', reset_time)
        
        self.add_suggestion("Wait for quota reset")
        self.add_suggestion("Upgrade service plan")
        self.add_suggestion("Use alternative service")


class ServiceUnavailableError(NetworkError):
    """服务不可用错误"""
    
    def __init__(
        self,
        service: str,
        url: str,
        maintenance_info: Optional[str] = None,
        **kwargs
    ):
        message = f"{service} service is unavailable"
        if maintenance_info:
            message += f": {maintenance_info}"
        
        super().__init__(
            message,
            url=url,
            status_code=503,
            recovery_action="Wait and retry later",
            max_retries=5,
            **kwargs
        )
        self.service = service
        self.maintenance_info = maintenance_info
        
        self.add_context('service', service)
        if maintenance_info:
            self.add_context('maintenance_info', maintenance_info)
        
        self.add_suggestion("Check service status page")
        self.add_suggestion("Try again later")
        self.add_suggestion("Use backup service if available")


class ProxyError(NetworkError):
    """代理错误"""
    
    def __init__(
        self,
        proxy_url: str,
        proxy_issue: str,
        **kwargs
    ):
        message = f"Proxy error with {proxy_url}: {proxy_issue}"
        super().__init__(message, **kwargs)
        self.proxy_url = proxy_url
        self.proxy_issue = proxy_issue
        
        self.add_context('proxy_url', proxy_url)
        self.add_context('proxy_issue', proxy_issue)
        
        self.add_suggestion("Check proxy configuration")
        self.add_suggestion("Try without proxy")
        self.add_suggestion("Use different proxy server")


class SSLError(NetworkError):
    """SSL/TLS错误"""
    
    def __init__(
        self,
        url: str,
        ssl_issue: str,
        **kwargs
    ):
        message = f"SSL error for {url}: {ssl_issue}"
        super().__init__(message, url=url, **kwargs)
        self.ssl_issue = ssl_issue
        
        self.add_context('ssl_issue', ssl_issue)
        
        self.add_suggestion("Check SSL certificate validity")
        self.add_suggestion("Update CA certificates")
        self.add_suggestion("Disable SSL verification (not recommended)")


class DNSError(NetworkError):
    """DNS解析错误"""
    
    def __init__(
        self,
        hostname: str,
        dns_issue: Optional[str] = None,
        **kwargs
    ):
        message = f"DNS resolution failed for {hostname}"
        if dns_issue:
            message += f": {dns_issue}"
        
        super().__init__(message, **kwargs)
        self.hostname = hostname
        self.dns_issue = dns_issue
        
        self.add_context('hostname', hostname)
        if dns_issue:
            self.add_context('dns_issue', dns_issue)
        
        self.add_suggestion("Check DNS settings")
        self.add_suggestion("Try different DNS server")
        self.add_suggestion("Check if hostname is correct")
