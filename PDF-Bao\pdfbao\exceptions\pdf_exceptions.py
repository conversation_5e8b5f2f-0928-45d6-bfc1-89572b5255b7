"""
PDF处理相关异常
"""

from typing import Optional, List
from pathlib import Path
from .base_exceptions import pdfbaoError, CriticalError, RecoverableError, ErrorSeverity


class PDFProcessingError(pdfbaoError):
    """PDF处理基础错误"""
    
    def __init__(
        self,
        message: str,
        file_path: Optional[Path] = None,
        page_number: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.file_path = file_path
        self.page_number = page_number
        
        if file_path:
            self.add_context('file_path', str(file_path))
            self.add_context('file_name', file_path.name)
            if file_path.exists():
                self.add_context('file_size', file_path.stat().st_size)
        
        if page_number:
            self.add_context('page_number', page_number)


class PDFParsingError(PDFProcessingError):
    """PDF解析错误"""
    
    def __init__(
        self,
        file_path: Path,
        parsing_stage: Optional[str] = None,
        **kwargs
    ):
        message = f"Failed to parse PDF: {file_path.name}"
        if parsing_stage:
            message += f" at stage: {parsing_stage}"
        
        super().__init__(message, file_path=file_path, **kwargs)
        self.parsing_stage = parsing_stage
        
        if parsing_stage:
            self.add_context('parsing_stage', parsing_stage)
        
        self.add_suggestion("Check if PDF file is corrupted")
        self.add_suggestion("Try opening PDF in a PDF viewer")
        self.add_suggestion("Convert PDF to a newer format")


class PDFCorruptedError(CriticalError):
    """PDF文件损坏错误"""
    
    def __init__(
        self,
        file_path: Path,
        corruption_details: Optional[str] = None,
        **kwargs
    ):
        message = f"PDF file is corrupted: {file_path.name}"
        if corruption_details:
            message += f" ({corruption_details})"
        
        super().__init__(message, **kwargs)
        self.file_path = file_path
        self.corruption_details = corruption_details
        
        self.add_context('file_path', str(file_path))
        if corruption_details:
            self.add_context('corruption_details', corruption_details)
        
        self.add_suggestion("Use PDF repair tools")
        self.add_suggestion("Re-download or re-create the PDF file")
        self.add_suggestion("Try converting from original source")


class PDFPermissionError(CriticalError):
    """PDF权限错误"""
    
    def __init__(
        self,
        file_path: Path,
        required_permission: str,
        **kwargs
    ):
        message = f"PDF permission denied: {required_permission} access to {file_path.name}"
        super().__init__(message, **kwargs)
        self.file_path = file_path
        self.required_permission = required_permission
        
        self.add_context('file_path', str(file_path))
        self.add_context('required_permission', required_permission)
        
        self.add_suggestion("Check if PDF is password protected")
        self.add_suggestion("Remove PDF restrictions using PDF tools")
        self.add_suggestion("Contact document owner for unrestricted version")


class PDFLayoutError(RecoverableError):
    """PDF布局分析错误"""
    
    def __init__(
        self,
        file_path: Path,
        page_number: int,
        layout_issue: str,
        **kwargs
    ):
        message = f"Layout analysis failed for page {page_number} in {file_path.name}: {layout_issue}"
        super().__init__(
            message,
            recovery_action="Try different layout analysis settings",
            max_retries=2,
            **kwargs
        )
        self.file_path = file_path
        self.page_number = page_number
        self.layout_issue = layout_issue
        
        self.add_context('file_path', str(file_path))
        self.add_context('page_number', page_number)
        self.add_context('layout_issue', layout_issue)
        
        self.add_suggestion("Skip problematic pages")
        self.add_suggestion("Use OCR workaround for scanned pages")
        self.add_suggestion("Try different document layout model")


class PDFFontError(RecoverableError):
    """PDF字体错误"""
    
    def __init__(
        self,
        font_name: str,
        font_issue: str,
        file_path: Optional[Path] = None,
        **kwargs
    ):
        message = f"Font error with '{font_name}': {font_issue}"
        if file_path:
            message += f" in {file_path.name}"
        
        super().__init__(
            message,
            recovery_action="Use fallback font",
            max_retries=1,
            **kwargs
        )
        self.font_name = font_name
        self.font_issue = font_issue
        self.file_path = file_path
        
        self.add_context('font_name', font_name)
        self.add_context('font_issue', font_issue)
        if file_path:
            self.add_context('file_path', str(file_path))
        
        self.add_suggestion("Install missing fonts")
        self.add_suggestion("Use font substitution")
        self.add_suggestion("Enable font fallback mode")


class PDFPageError(PDFProcessingError):
    """PDF页面处理错误"""
    
    def __init__(
        self,
        file_path: Path,
        page_number: int,
        page_issue: str,
        **kwargs
    ):
        message = f"Page {page_number} error in {file_path.name}: {page_issue}"
        super().__init__(message, file_path=file_path, page_number=page_number, **kwargs)
        self.page_issue = page_issue
        
        self.add_context('page_issue', page_issue)
        
        self.add_suggestion("Skip problematic page")
        self.add_suggestion("Extract page separately")
        self.add_suggestion("Check page content manually")


class PDFTextExtractionError(RecoverableError):
    """PDF文本提取错误"""
    
    def __init__(
        self,
        file_path: Path,
        page_number: Optional[int] = None,
        extraction_method: Optional[str] = None,
        **kwargs
    ):
        message = f"Text extraction failed for {file_path.name}"
        if page_number:
            message += f" page {page_number}"
        if extraction_method:
            message += f" using {extraction_method}"
        
        super().__init__(
            message,
            recovery_action="Try OCR extraction",
            max_retries=2,
            **kwargs
        )
        self.file_path = file_path
        self.page_number = page_number
        self.extraction_method = extraction_method
        
        self.add_context('file_path', str(file_path))
        if page_number:
            self.add_context('page_number', page_number)
        if extraction_method:
            self.add_context('extraction_method', extraction_method)
        
        self.add_suggestion("Enable OCR mode")
        self.add_suggestion("Check if page contains images only")
        self.add_suggestion("Try different text extraction method")


class PDFImageError(pdfbaoError):
    """PDF图像处理错误"""
    
    def __init__(
        self,
        file_path: Path,
        page_number: int,
        image_issue: str,
        **kwargs
    ):
        message = f"Image processing error on page {page_number} of {file_path.name}: {image_issue}"
        super().__init__(message, severity=ErrorSeverity.LOW, **kwargs)
        self.file_path = file_path
        self.page_number = page_number
        self.image_issue = image_issue
        
        self.add_context('file_path', str(file_path))
        self.add_context('page_number', page_number)
        self.add_context('image_issue', image_issue)
        
        self.add_suggestion("Skip image processing")
        self.add_suggestion("Reduce image quality")
        self.add_suggestion("Check available memory")


class PDFTableError(RecoverableError):
    """PDF表格处理错误"""
    
    def __init__(
        self,
        file_path: Path,
        page_number: int,
        table_index: Optional[int] = None,
        **kwargs
    ):
        message = f"Table processing error on page {page_number} of {file_path.name}"
        if table_index is not None:
            message += f" (table {table_index})"
        
        super().__init__(
            message,
            recovery_action="Skip table or use simplified processing",
            max_retries=1,
            **kwargs
        )
        self.file_path = file_path
        self.page_number = page_number
        self.table_index = table_index
        
        self.add_context('file_path', str(file_path))
        self.add_context('page_number', page_number)
        if table_index is not None:
            self.add_context('table_index', table_index)
        
        self.add_suggestion("Disable table detection")
        self.add_suggestion("Process table as regular text")
        self.add_suggestion("Manual table extraction")
