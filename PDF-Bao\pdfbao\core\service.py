"""
pdfbao 核心服务
集成新的配置管理和错误处理系统的示例
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Any, List

from ..config import ConfigManager, pdfbaoSettings, get_settings
from ..exceptions import (
    pdfbaoError,
    TranslationError,
    PDFProcessingError,
    ConfigurationError,
    ErrorHandler,
    ErrorContext,
    RetryStrategy,
    BackoffStrategy,
    get_error_handler,
    get_error_reporter,
    with_error_handling,
    with_async_error_handling
)
from ..translator.translator import BaseTranslator, OpenAITranslator
from ..format.pdf.translation_config import TranslationConfig

logger = logging.getLogger(__name__)


class pdfbaoService:
    """pdfbao 核心服务类"""
    
    def __init__(self, config_file: Optional[Path] = None):
        self.config_manager = ConfigManager(config_file)
        self.settings: Optional[pdfbaoSettings] = None
        self.translator: Optional[BaseTranslator] = None
        self.error_handler = get_error_handler()
        self.error_reporter = get_error_reporter()
        
        # 设置错误处理策略
        self._setup_error_handling()
    
    def initialize(self, cli_args: Optional[Dict[str, Any]] = None) -> bool:
        """初始化服务"""
        try:
            # 加载配置
            self.settings = self.config_manager.load_config(cli_args)
            
            # 验证配置
            is_valid, errors = self.config_manager.validate_current_config()
            if not is_valid:
                for error in errors:
                    logger.error(f"Configuration error: {error}")
                    self.error_reporter.report_error(
                        error, 
                        component="configuration",
                        operation="validation"
                    )
                return False
            
            # 初始化翻译器
            self._initialize_translator()
            
            logger.info("pdfbao service initialized successfully")
            return True
            
        except Exception as e:
            error_context = ErrorContext(
                operation="service_initialization",
                component="core_service"
            )
            self.error_handler.handle_error(e, error_context)
            return False
    
    @with_error_handling(operation="translator_initialization", component="translator")
    def _initialize_translator(self):
        """初始化翻译器"""
        if not self.settings:
            raise ConfigurationError("Settings not loaded")
        
        api_settings = self.settings.api
        if not api_settings.api_key:
            raise ConfigurationError(
                "API key not configured",
                config_section="api",
                config_key="api_key"
            ).add_suggestion("Set API key in configuration or environment variable")
        
        try:
            self.translator = OpenAITranslator(
                lang_in=self.settings.translation.lang_in,
                lang_out=self.settings.translation.lang_out,
                ignore_cache=False,
                base_url=api_settings.base_url,
                api_key=api_settings.api_key,
                model=api_settings.model
            )
            logger.info(f"Translator initialized: {api_settings.model}")
            
        except Exception as e:
            raise TranslationError(
                "Failed to initialize translator",
                cause=e
            ).add_context("model", api_settings.model).add_context("base_url", api_settings.base_url)
    
    @with_error_handling(operation="pdf_translation", component="pdf_processor")
    def translate_pdf(
        self, 
        input_file: Path, 
        output_dir: Optional[Path] = None
    ) -> Dict[str, Any]:
        """翻译PDF文件"""
        if not self.settings or not self.translator:
            raise pdfbaoError("Service not properly initialized")
        
        # 验证输入文件
        if not input_file.exists():
            raise PDFProcessingError(
                f"Input file does not exist: {input_file}",
                file_path=input_file
            ).add_suggestion("Check file path and permissions")
        
        if not input_file.suffix.lower() == '.pdf':
            raise PDFProcessingError(
                f"Input file is not a PDF: {input_file}",
                file_path=input_file
            ).add_suggestion("Ensure file has .pdf extension")
        
        # 设置输出目录
        if not output_dir:
            output_dir = self.settings.output.output_dir or input_file.parent
        
        # 创建翻译配置
        translation_config = self._create_translation_config(input_file, output_dir)
        
        # 执行翻译
        try:
            # 这里应该调用实际的翻译逻辑
            # result = await self._perform_translation(translation_config)
            
            # 模拟翻译结果
            result = {
                "input_file": str(input_file),
                "output_dir": str(output_dir),
                "status": "success",
                "pages_translated": 10,
                "translation_time": 120.5
            }
            
            logger.info(f"PDF translation completed: {input_file}")
            return result
            
        except Exception as e:
            # 错误会被装饰器自动处理
            raise
    
    def _create_translation_config(
        self, 
        input_file: Path, 
        output_dir: Path
    ) -> TranslationConfig:
        """创建翻译配置"""
        if not self.settings or not self.translator:
            raise pdfbaoError("Service not initialized")
        
        # 从设置创建翻译配置
        config = TranslationConfig(
            input_file=str(input_file),
            translator=self.translator,
            lang_in=self.settings.translation.lang_in,
            lang_out=self.settings.translation.lang_out,
            doc_layout_model=None,  # 需要初始化
            pages=self.settings.translation.pages,
            output_dir=str(output_dir),
            debug=self.settings.debug,
            no_dual=self.settings.output.no_dual,
            no_mono=self.settings.output.no_mono,
            qps=self.settings.performance.qps,
            pool_max_workers=self.settings.performance.pool_max_workers,
            min_text_length=self.settings.translation.min_text_length,
            skip_clean=self.settings.translation.skip_clean,
            enhance_compatibility=self.settings.translation.enhance_compatibility,
            disable_rich_text_translate=self.settings.translation.disable_rich_text_translate,
            watermark_output_mode=self.settings.output.watermark_mode,
            auto_extract_glossary=self.settings.translation.auto_extract_glossary,
            split_short_lines=self.settings.translation.split_short_lines,
            short_line_split_factor=self.settings.translation.short_line_split_factor
        )
        
        return config
    
    def _setup_error_handling(self):
        """设置错误处理策略"""
        # 翻译错误重试策略
        translation_retry = RetryStrategy(
            max_attempts=3,
            base_delay=1.0,
            max_delay=30.0,
            backoff_strategy=BackoffStrategy.EXPONENTIAL,
            backoff_multiplier=2.0
        )
        self.error_handler.register_retry_strategy(TranslationError, translation_retry)
        
        # PDF处理错误重试策略
        pdf_retry = RetryStrategy(
            max_attempts=2,
            base_delay=0.5,
            max_delay=10.0,
            backoff_strategy=BackoffStrategy.LINEAR
        )
        self.error_handler.register_retry_strategy(PDFProcessingError, pdf_retry)
        
        # 注册错误回调
        self.error_handler.register_error_callback(
            pdfbaoError,
            self._on_error_callback
        )
    
    def _on_error_callback(self, error: Exception, context: ErrorContext):
        """错误回调函数"""
        # 报告错误
        self.error_reporter.report_error(
            error,
            component=context.component,
            operation=context.operation,
            context=context.user_data
        )
        
        # 记录用户友好的错误信息
        if isinstance(error, pdfbaoError):
            user_message = f"操作失败: {error.message}"
            if error.suggestions:
                user_message += f"\n建议: {'; '.join(error.suggestions)}"
            logger.info(f"User notification: {user_message}")
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        status = {
            "initialized": self.settings is not None,
            "translator_ready": self.translator is not None,
            "configuration_valid": False,
            "error_stats": self.error_handler.get_error_stats(),
            "recent_errors": []
        }
        
        if self.settings:
            is_valid, errors = self.config_manager.validate_current_config()
            status["configuration_valid"] = is_valid
            status["configuration_summary"] = self.config_manager.get_config_summary()
        
        # 获取最近的错误
        recent_reports = self.error_reporter.get_reports(limit=5)
        status["recent_errors"] = [
            {
                "id": r.error_id,
                "type": r.error_type,
                "message": r.error_message,
                "timestamp": r.timestamp.isoformat()
            }
            for r in recent_reports
        ]
        
        return status
    
    def update_configuration(self, updates: Dict[str, Any]) -> bool:
        """更新配置"""
        try:
            success = self.config_manager.update_settings(updates)
            if success:
                self.settings = self.config_manager.get_settings()
                # 重新初始化翻译器（如果API设置改变）
                if any(key.startswith('api.') for key in updates.keys()):
                    self._initialize_translator()
            return success
        except Exception as e:
            self.error_reporter.report_error(
                e,
                component="configuration",
                operation="update_configuration"
            )
            return False
    
    def export_error_report(self, format: str = "json") -> Optional[str]:
        """导出错误报告"""
        try:
            from ..exceptions.error_reporter import ReportFormat
            report_format = ReportFormat(format.lower())
            return self.error_reporter.export_reports(report_format)
        except Exception as e:
            logger.error(f"Failed to export error report: {e}")
            return None


# 全局服务实例
_service: Optional[pdfbaoService] = None


def get_service() -> pdfbaoService:
    """获取全局服务实例"""
    global _service
    if _service is None:
        _service = pdfbaoService()
    return _service


def initialize_service(config_file: Optional[Path] = None, cli_args: Optional[Dict[str, Any]] = None) -> bool:
    """初始化全局服务"""
    global _service
    _service = pdfbaoService(config_file)
    return _service.initialize(cli_args)
