#!/usr/bin/env python3
"""
PDF-Bao GUI - 现代化图形界面版本的PDF翻译工具
基于CustomTkinter构建的美观用户界面
"""

import asyncio
import json
import logging
import os
import sys
import threading
import time
import tkinter as tk
from pathlib import Path
from tkinter import filedialog, messagebox
from typing import Dict, List, Optional

# 导入环境变量管理
try:
    from dotenv import load_dotenv
    # 加载根目录的.env文件
    env_path = Path(__file__).parent.parent / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✅ 已加载环境变量配置: {env_path}")
    else:
        print("⚠️ 未找到.env文件，使用默认配置")
except ImportError:
    print("⚠️ 未安装python-dotenv，无法加载.env文件。请运行: pip install python-dotenv")

# 导入CustomTkinter
try:
    import customtkinter as ctk
except ImportError:
    print("错误：无法导入CustomTkinter。请安装：pip install customtkinter")
    sys.exit(1)

# 确保能够导入pdfbao
try:
    from pdfbao.time_tracker import translation_time_tracker
    import pdfbao.format.pdf.high_level
    from pdfbao.format.pdf.translation_config import TranslationConfig, WatermarkOutputMode
    from pdfbao.translator.translator import OpenAITranslator, set_translate_rate_limiter
    from pdfbao.docvision.doclayout import DocLayoutModel
    from pdfbao.glossary import Glossary
    import pdfbao.assets.assets
except ImportError as e:
    print(f"错误：无法导入PDF-Bao库。请确保已正确安装PDF-Bao。\n错误详情：{e}")
    sys.exit(1)

# 设置CustomTkinter外观
ctk.set_appearance_mode("dark")  # 可选: "dark", "light", "system"
ctk.set_default_color_theme("blue")  # 可选: "blue", "green", "dark-blue"


class ModernPDFBaoGUI:
    def __init__(self):
        # 设置主题和外观
        ctk.set_appearance_mode("light")  # 默认浅色主题
        ctk.set_default_color_theme("blue")  # 使用蓝色主题

        self.root = ctk.CTk()
        self.root.title("🐼 PDF-Bao - 智能PDF翻译工具")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)

        # 设置窗口图标和样式
        try:
            # 设置窗口图标
            icon_path = Path(__file__).parent / "assets" / "logo.ico"
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except Exception as e:
            logging.warning(f"设置窗口图标失败: {e}")

        # 配置变量
        self.config_vars = {}
        self.selected_files = []
        self.translation_thread = None
        self.translation_running = False
        self.time_update_running = False
        self.time_update_thread = None
        self.current_theme = "light"

        # 设置日志
        self.setup_logging()

        # 创建界面
        self.create_widgets()

        # 从.env文件加载默认配置
        self.load_env_config()

        # 加载用户配置（会覆盖.env中的配置）
        self.load_config()
        
    def set_concurrent_preset(self, qps: int, threads: int):
        """设置并发预设值"""
        self.config_vars['qps'].set(str(qps))
        self.config_vars['pool_max_workers'].set(str(threads))
        
        # 显示设置完成提示
        logging.info(f"🚀 已设置并发参数: QPS={qps}, 线程数={threads}")
    
    def load_env_config(self):
        """从.env文件加载默认配置"""
        try:
            # 根据您的.env文件格式解析配置
            env_path = Path(__file__).parent.parent / '.env'
            if env_path.exists():
                with open(env_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 解析.env文件
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if ':' in line:
                            key, value = line.split(':', 1)
                            key = key.strip()
                            value = value.strip()
                            
                            # 映射.env中的配置到GUI变量
                            if key == 'base_url':
                                self.config_vars['base_url'].set(value)
                            elif key == 'api':
                                self.config_vars['api_key'].set(value)
                            elif key == 'model':
                                self.config_vars['model'].set(value)
                
                logging.info(f"已从 {env_path} 加载环境配置")
        except Exception as e:
            logging.warning(f"加载.env配置失败: {str(e)}")
        
    def setup_logging(self):
        """设置日志系统"""
        class GUILogHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget
                
            def emit(self, record):
                try:
                    msg = self.format(record)
                    self.text_widget.insert("end", msg + "\n")
                    self.text_widget.see("end")
                except:
                    pass
        
        # 稍后会设置
        self.log_handler = None
        
    def create_widgets(self):
        """创建主界面"""
        # 配置网格权重
        self.root.grid_columnconfigure(1, weight=1)
        self.root.grid_rowconfigure(1, weight=1)  # 主内容区域

        # 创建顶部标题栏
        self.create_header()

        # 创建侧边栏
        self.create_sidebar()

        # 创建主内容区域
        self.create_main_content()

    def create_header(self):
        """创建顶部标题栏"""
        self.header_frame = ctk.CTkFrame(self.root, height=70, corner_radius=0)
        self.header_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=0, pady=0)
        self.header_frame.grid_columnconfigure(1, weight=1)
        self.header_frame.grid_propagate(False)  # 固定高度

        # 应用图标和标题
        title_frame = ctk.CTkFrame(self.header_frame, fg_color="transparent")
        title_frame.grid(row=0, column=0, sticky="w", padx=20, pady=15)

        # Logo和标题水平布局
        logo_title_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        logo_title_frame.grid(row=0, column=0, sticky="w")

        # 尝试加载Logo
        self.logo_label = None
        try:
            from PIL import Image

            logo_path = Path(__file__).parent / "assets" / "logo_32.png"
            if logo_path.exists():
                # 加载Logo图片
                logo_image = Image.open(logo_path)
                logo_ctk_image = ctk.CTkImage(light_image=logo_image, dark_image=logo_image, size=(32, 32))

                self.logo_label = ctk.CTkLabel(
                    logo_title_frame,
                    image=logo_ctk_image,
                    text=""
                )
                self.logo_label.grid(row=0, column=0, padx=(0, 10), pady=0)
        except Exception as e:
            logging.warning(f"加载Logo失败: {e}")

        # 标题文字
        text_frame = ctk.CTkFrame(logo_title_frame, fg_color="transparent")
        text_frame.grid(row=0, column=1, sticky="w")

        title_label = ctk.CTkLabel(
            text_frame,
            text="PDF-Bao",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, sticky="w")

        subtitle_label = ctk.CTkLabel(
            text_frame,
            text="智能PDF翻译工具 - 让语言不再是障碍",
            font=ctk.CTkFont(size=12),
            text_color=("gray60", "gray40")
        )
        subtitle_label.grid(row=1, column=0, sticky="w")

        # 主题切换和设置按钮
        controls_frame = ctk.CTkFrame(self.header_frame, fg_color="transparent")
        controls_frame.grid(row=0, column=1, sticky="e", padx=20, pady=15)

        # 主题切换按钮
        self.theme_button = ctk.CTkButton(
            controls_frame,
            text="🌙 深色",
            width=80,
            height=32,
            command=self.toggle_theme,
            font=ctk.CTkFont(size=12)
        )
        self.theme_button.grid(row=0, column=0, padx=(0, 10))

        # 设置按钮
        settings_button = ctk.CTkButton(
            controls_frame,
            text="⚙️ 设置",
            width=80,
            height=32,
            command=self.show_settings,
            font=ctk.CTkFont(size=12)
        )
        settings_button.grid(row=0, column=1)
        
    def create_sidebar(self):
        """创建侧边栏导航"""
        self.sidebar_frame = ctk.CTkFrame(self.root, width=280, corner_radius=0)
        self.sidebar_frame.grid(row=1, column=0, sticky="nsew", padx=0, pady=0)
        self.sidebar_frame.grid_rowconfigure(8, weight=1)  # 让底部有弹性空间
        self.sidebar_frame.grid_propagate(False)  # 固定宽度
        
        # 侧边栏标题
        sidebar_title = ctk.CTkLabel(
            self.sidebar_frame,
            text="📋 导航菜单",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        sidebar_title.grid(row=0, column=0, padx=20, pady=(30, 20))
        
        # 导航按钮
        self.nav_buttons = {}
        nav_items = [
            ("📁 文件管理", "files", "管理要翻译的PDF文件"),
            ("⚙️ 翻译设置", "config", "配置翻译参数"),
            ("🔧 高级选项", "advanced", "高级功能设置"),
            ("📊 翻译进度", "progress", "查看翻译进度"),
            ("📋 运行日志", "logs", "查看详细日志")
        ]

        for i, (text, key, tooltip) in enumerate(nav_items):
            btn = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                width=240,
                height=45,
                command=lambda k=key: self.show_tab(k),
                font=ctk.CTkFont(size=14, weight="bold"),
                corner_radius=10,
                hover_color=("gray70", "gray30")
            )
            btn.grid(row=i+1, column=0, padx=20, pady=8, sticky="ew")
            self.nav_buttons[key] = btn
        
        # 分隔线
        separator = ctk.CTkFrame(self.sidebar_frame, height=2, fg_color=("gray80", "gray20"))
        separator.grid(row=7, column=0, sticky="ew", padx=20, pady=20)
        
        # 底部控制按钮
        self.start_button = ctk.CTkButton(
            self.sidebar_frame,
            text="🚀 开始翻译",
            width=240,
            height=55,
            command=self.start_translation,
            font=ctk.CTkFont(size=16, weight="bold"),
            fg_color=("#2E8B57", "#228B22"),  # 海绿色
            hover_color=("#3CB371", "#32CD32"),
            corner_radius=12
        )
        self.start_button.grid(row=8, column=0, padx=20, pady=10, sticky="ew")

        self.stop_button = ctk.CTkButton(
            self.sidebar_frame,
            text="⏹️ 停止翻译",
            width=240,
            height=45,
            command=self.stop_translation,
            state="disabled",
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#DC143C", "#B22222"),  # 深红色
            hover_color=("#FF6347", "#CD5C5C"),
            corner_radius=10
        )
        self.stop_button.grid(row=9, column=0, padx=20, pady=5, sticky="ew")

        self.save_config_button = ctk.CTkButton(
            self.sidebar_frame,
            text="💾 保存配置",
            width=240,
            height=40,
            command=self.save_config,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color=("#4682B4", "#4169E1"),  # 钢蓝色
            hover_color=("#5F9EA0", "#6495ED"),
            corner_radius=10
        )
        self.save_config_button.grid(row=10, column=0, padx=20, pady=(5, 30), sticky="ew")
        
    def create_main_content(self):
        """创建主内容区域"""
        # 主内容框架
        self.main_frame = ctk.CTkFrame(self.root, corner_radius=15)
        self.main_frame.grid(row=1, column=1, sticky="nsew", padx=20, pady=20)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)
        
        # 创建各个标签页内容
        self.create_files_tab()
        self.create_config_tab()
        self.create_advanced_tab()
        self.create_progress_tab()
        self.create_logs_tab()
        
        # 默认显示文件管理标签页
        self.current_tab = "files"
        self.show_tab("files")
        
    def create_files_tab(self):
        """创建文件管理标签页"""
        self.files_frame = ctk.CTkFrame(self.main_frame)
        self.files_frame.grid_columnconfigure(0, weight=1)
        self.files_frame.grid_rowconfigure(1, weight=1)
        
        # 标题和描述
        title_label = ctk.CTkLabel(
            self.files_frame,
            text="📁 文件管理",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=30, pady=(30, 5), sticky="w")

        desc_label = ctk.CTkLabel(
            self.files_frame,
            text="选择要翻译的PDF文件，支持批量处理",
            font=ctk.CTkFont(size=14),
            text_color=("gray60", "gray40")
        )
        desc_label.grid(row=1, column=0, padx=30, pady=(0, 20), sticky="w")

        # 文件列表框架
        files_list_frame = ctk.CTkFrame(self.files_frame, corner_radius=15)
        files_list_frame.grid(row=2, column=0, padx=30, pady=15, sticky="nsew")
        files_list_frame.grid_columnconfigure(0, weight=1)
        files_list_frame.grid_rowconfigure(0, weight=1)

        # 文件列表标题
        list_title = ctk.CTkLabel(
            files_list_frame,
            text="📋 待翻译文件列表",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        list_title.grid(row=0, column=0, padx=20, pady=(15, 5), sticky="w")

        # 文件列表(使用CTkTextbox模拟列表)
        self.files_textbox = ctk.CTkTextbox(
            files_list_frame,
            height=250,
            corner_radius=10,
            font=ctk.CTkFont(size=12)
        )
        self.files_textbox.grid(row=1, column=0, padx=20, pady=(0, 15), sticky="nsew")
        
        # 文件操作按钮框架
        files_buttons_frame = ctk.CTkFrame(self.files_frame, fg_color="transparent")
        files_buttons_frame.grid(row=3, column=0, padx=30, pady=15, sticky="ew")
        files_buttons_frame.grid_columnconfigure((0, 1, 2), weight=1)

        add_files_btn = ctk.CTkButton(
            files_buttons_frame,
            text="➕ 添加文件",
            command=self.add_files,
            width=150,
            height=45,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#1f538d", "#14375e"),
            hover_color=("#2563eb", "#1d4ed8"),
            corner_radius=12
        )
        add_files_btn.grid(row=0, column=0, padx=10, pady=10)

        clear_files_btn = ctk.CTkButton(
            files_buttons_frame,
            text="🗑️ 清空列表",
            command=self.clear_files,
            width=150,
            height=45,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#dc2626", "#991b1b"),
            hover_color=("#ef4444", "#dc2626"),
            corner_radius=12
        )
        clear_files_btn.grid(row=0, column=1, padx=10, pady=10)

        # 添加预览按钮
        preview_btn = ctk.CTkButton(
            files_buttons_frame,
            text="👁️ 预览文件",
            command=self.preview_files,
            width=150,
            height=45,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#059669", "#047857"),
            hover_color=("#10b981", "#059669"),
            corner_radius=12
        )
        preview_btn.grid(row=0, column=2, padx=10, pady=10)
        
        # 输出目录设置
        output_frame = ctk.CTkFrame(self.files_frame, corner_radius=15)
        output_frame.grid(row=4, column=0, padx=30, pady=15, sticky="ew")
        output_frame.grid_columnconfigure(0, weight=1)

        output_label = ctk.CTkLabel(
            output_frame,
            text="📂 输出目录设置",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        output_label.grid(row=0, column=0, padx=20, pady=(15, 5), sticky="w")

        output_desc = ctk.CTkLabel(
            output_frame,
            text="选择翻译后的PDF文件保存位置",
            font=ctk.CTkFont(size=12),
            text_color=("gray60", "gray40")
        )
        output_desc.grid(row=1, column=0, padx=20, pady=(0, 10), sticky="w")

        output_entry_frame = ctk.CTkFrame(output_frame, fg_color="transparent")
        output_entry_frame.grid(row=2, column=0, padx=20, pady=(0, 15), sticky="ew")
        output_entry_frame.grid_columnconfigure(0, weight=1)
        
        self.output_dir_var = tk.StringVar()
        self.output_entry = ctk.CTkEntry(
            output_entry_frame, 
            textvariable=self.output_dir_var,
            placeholder_text="选择输出目录..."
        )
        self.output_entry.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        
        browse_btn = ctk.CTkButton(
            output_entry_frame,
            text="浏览",
            command=self.browse_output_dir,
            width=80
        )
        browse_btn.grid(row=0, column=1, padx=(0, 10), pady=10)
        
    def create_config_tab(self):
        """创建翻译配置标签页"""
        self.config_frame = ctk.CTkFrame(self.main_frame)
        self.config_frame.grid_columnconfigure(0, weight=1)
        
        # 标题
        title_label = ctk.CTkLabel(
            self.config_frame, 
            text="⚙️ 翻译设置", 
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")
        
        # 滚动框架
        self.config_scroll = ctk.CTkScrollableFrame(self.config_frame, height=500)
        self.config_scroll.grid(row=1, column=0, padx=20, pady=10, sticky="nsew")
        self.config_scroll.grid_columnconfigure(1, weight=1)
        
        # OpenAI配置
        openai_label = ctk.CTkLabel(self.config_scroll, text="🤖 OpenAI配置", font=ctk.CTkFont(size=16, weight="bold"))
        openai_label.grid(row=0, column=0, columnspan=2, padx=10, pady=(10, 5), sticky="w")
        
        # API Key
        ctk.CTkLabel(self.config_scroll, text="API Key:").grid(row=1, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['api_key'] = tk.StringVar(value="sk-3BVZyBBs64JYc8R1334eIXVSivrjs8kVKT9q4VGw75L23aW3")
        api_key_entry = ctk.CTkEntry(
            self.config_scroll, 
            textvariable=self.config_vars['api_key'],
            show="*",
            placeholder_text="输入您的API Key"
        )
        api_key_entry.grid(row=1, column=1, padx=10, pady=5, sticky="ew")
        
        # Base URL
        ctk.CTkLabel(self.config_scroll, text="Base URL:").grid(row=2, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['base_url'] = tk.StringVar(value="http://192.168.2.118:3000/v1")
        base_url_entry = ctk.CTkEntry(self.config_scroll, textvariable=self.config_vars['base_url'])
        base_url_entry.grid(row=2, column=1, padx=10, pady=5, sticky="ew")
        
        # 模型选择
        ctk.CTkLabel(self.config_scroll, text="模型:").grid(row=3, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['model'] = tk.StringVar(value="gemini-2.0-flash")
        model_combo = ctk.CTkComboBox(
            self.config_scroll, 
            variable=self.config_vars['model'],
            values=["gemini-2.0-flash", "gpt-4o", "gpt-4o-mini", "gpt-4", "gpt-3.5-turbo"]
        )
        model_combo.grid(row=3, column=1, padx=10, pady=5, sticky="ew")
        
        # 语言配置
        lang_label = ctk.CTkLabel(self.config_scroll, text="🌍 语言配置", font=ctk.CTkFont(size=16, weight="bold"))
        lang_label.grid(row=4, column=0, columnspan=2, padx=10, pady=(20, 5), sticky="w")
        
        # 源语言
        ctk.CTkLabel(self.config_scroll, text="源语言:").grid(row=5, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['lang_in'] = tk.StringVar(value="en")
        lang_in_combo = ctk.CTkComboBox(
            self.config_scroll, 
            variable=self.config_vars['lang_in'],
            values=["en", "zh", "ja", "ko", "fr", "de", "es", "ru"]
        )
        lang_in_combo.grid(row=5, column=1, padx=10, pady=5, sticky="ew")
        
        # 目标语言
        ctk.CTkLabel(self.config_scroll, text="目标语言:").grid(row=6, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['lang_out'] = tk.StringVar(value="zh")
        lang_out_combo = ctk.CTkComboBox(
            self.config_scroll, 
            variable=self.config_vars['lang_out'],
            values=["zh", "en", "ja", "ko", "fr", "de", "es", "ru"]
        )
        lang_out_combo.grid(row=6, column=1, padx=10, pady=5, sticky="ew")
        
        # 其他配置
        other_label = ctk.CTkLabel(self.config_scroll, text="🔧 其他设置", font=ctk.CTkFont(size=16, weight="bold"))
        other_label.grid(row=7, column=0, columnspan=2, padx=10, pady=(20, 5), sticky="w")
        
        # 并发设置
        concurrent_label = ctk.CTkLabel(self.config_scroll, text="⚡ 并发设置", font=ctk.CTkFont(size=14, weight="bold"))
        concurrent_label.grid(row=8, column=0, columnspan=2, padx=10, pady=(10, 5), sticky="w")
        
        # QPS限制
        ctk.CTkLabel(self.config_scroll, text="API请求速率(QPS):").grid(row=9, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['qps'] = tk.StringVar(value="10")
        qps_frame = ctk.CTkFrame(self.config_scroll, fg_color="transparent")
        qps_frame.grid(row=9, column=1, padx=10, pady=5, sticky="ew")
        qps_frame.grid_columnconfigure(0, weight=1)
        
        qps_entry = ctk.CTkEntry(qps_frame, textvariable=self.config_vars['qps'], width=80)
        qps_entry.grid(row=0, column=0, sticky="w")
        
        qps_help = ctk.CTkLabel(qps_frame, text="(每秒API调用次数)", font=ctk.CTkFont(size=10))
        qps_help.grid(row=0, column=1, padx=(10, 0), sticky="w")
        
        # 线程池大小
        ctk.CTkLabel(self.config_scroll, text="并发线程数:").grid(row=10, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['pool_max_workers'] = tk.StringVar(value="20")
        pool_frame = ctk.CTkFrame(self.config_scroll, fg_color="transparent")
        pool_frame.grid(row=10, column=1, padx=10, pady=5, sticky="ew")
        pool_frame.grid_columnconfigure(0, weight=1)
        
        pool_entry = ctk.CTkEntry(pool_frame, textvariable=self.config_vars['pool_max_workers'], width=80)
        pool_entry.grid(row=0, column=0, sticky="w")
        
        pool_help = ctk.CTkLabel(pool_frame, text="(同时处理的段落数)", font=ctk.CTkFont(size=10))
        pool_help.grid(row=0, column=1, padx=(10, 0), sticky="w")
        
        # 预设按钮
        preset_frame = ctk.CTkFrame(self.config_scroll, fg_color="transparent")
        preset_frame.grid(row=11, column=0, columnspan=2, padx=10, pady=10, sticky="ew")
        
        ctk.CTkLabel(preset_frame, text="快速设置:", font=ctk.CTkFont(size=12, weight="bold")).grid(row=0, column=0, padx=(0, 10), sticky="w")
        
        conservative_btn = ctk.CTkButton(
            preset_frame, text="保守(QPS:5, 线程:10)", width=150, height=28,
            command=lambda: self.set_concurrent_preset(5, 10)
        )
        conservative_btn.grid(row=0, column=1, padx=5, sticky="w")
        
        balanced_btn = ctk.CTkButton(
            preset_frame, text="平衡(QPS:15, 线程:30)", width=150, height=28,
            command=lambda: self.set_concurrent_preset(15, 30)
        )
        balanced_btn.grid(row=0, column=2, padx=5, sticky="w")
        
        aggressive_btn = ctk.CTkButton(
            preset_frame, text="激进(QPS:30, 线程:50)", width=150, height=28,
            command=lambda: self.set_concurrent_preset(30, 50)
        )
        aggressive_btn.grid(row=0, column=3, padx=5, sticky="w")
        
        # 页面范围
        ctk.CTkLabel(self.config_scroll, text="页面范围:").grid(row=12, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['pages'] = tk.StringVar()
        pages_entry = ctk.CTkEntry(
            self.config_scroll, 
            textvariable=self.config_vars['pages'],
            placeholder_text="例如: 1,2,3-5 或留空翻译全部"
        )
        pages_entry.grid(row=12, column=1, padx=10, pady=5, sticky="ew")
        
    def create_advanced_tab(self):
        """创建高级选项标签页"""
        self.advanced_frame = ctk.CTkFrame(self.main_frame)
        self.advanced_frame.grid_columnconfigure(0, weight=1)
        
        # 标题
        title_label = ctk.CTkLabel(
            self.advanced_frame, 
            text="🔧 高级选项", 
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")
        
        # 滚动框架
        self.advanced_scroll = ctk.CTkScrollableFrame(self.advanced_frame, height=500)
        self.advanced_scroll.grid(row=1, column=0, padx=20, pady=10, sticky="nsew")
        
        # 输出选项
        output_label = ctk.CTkLabel(self.advanced_scroll, text="📄 输出选项", font=ctk.CTkFont(size=16, weight="bold"))
        output_label.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="w")
        
        # 复选框选项
        self.config_vars['no_dual'] = tk.BooleanVar()
        no_dual_cb = ctk.CTkCheckBox(self.advanced_scroll, text="不输出双语PDF", variable=self.config_vars['no_dual'])
        no_dual_cb.grid(row=1, column=0, padx=10, pady=5, sticky="w")
        
        self.config_vars['no_mono'] = tk.BooleanVar()
        no_mono_cb = ctk.CTkCheckBox(self.advanced_scroll, text="不输出单语PDF", variable=self.config_vars['no_mono'])
        no_mono_cb.grid(row=2, column=0, padx=10, pady=5, sticky="w")
        
        self.config_vars['ignore_cache'] = tk.BooleanVar()
        ignore_cache_cb = ctk.CTkCheckBox(self.advanced_scroll, text="忽略缓存", variable=self.config_vars['ignore_cache'])
        ignore_cache_cb.grid(row=3, column=0, padx=10, pady=5, sticky="w")
        
        self.config_vars['skip_clean'] = tk.BooleanVar()
        skip_clean_cb = ctk.CTkCheckBox(self.advanced_scroll, text="跳过PDF清理", variable=self.config_vars['skip_clean'])
        skip_clean_cb.grid(row=4, column=0, padx=10, pady=5, sticky="w")
        
        # 水印选项
        watermark_label = ctk.CTkLabel(self.advanced_scroll, text="🏷️ 水印选项", font=ctk.CTkFont(size=16, weight="bold"))
        watermark_label.grid(row=5, column=0, padx=10, pady=(20, 5), sticky="w")
        
        watermark_frame = ctk.CTkFrame(self.advanced_scroll)
        watermark_frame.grid(row=6, column=0, padx=10, pady=5, sticky="ew")
        watermark_frame.grid_columnconfigure(1, weight=1)
        
        ctk.CTkLabel(watermark_frame, text="水印模式:").grid(row=0, column=0, padx=10, pady=10, sticky="w")
        self.config_vars['watermark_mode'] = tk.StringVar(value="no_watermark")  # 默认无水印
        watermark_combo = ctk.CTkComboBox(
            watermark_frame, 
            variable=self.config_vars['watermark_mode'],
            values=["no_watermark", "watermarked", "both"]
        )
        watermark_combo.grid(row=0, column=1, padx=10, pady=10, sticky="ew")
        
        # 兼容性选项
        compat_label = ctk.CTkLabel(self.advanced_scroll, text="🛡️ 兼容性选项", font=ctk.CTkFont(size=16, weight="bold"))
        compat_label.grid(row=7, column=0, padx=10, pady=(20, 5), sticky="w")
        
        self.config_vars['enhance_compatibility'] = tk.BooleanVar()
        enhance_compat_cb = ctk.CTkCheckBox(self.advanced_scroll, text="增强兼容性", variable=self.config_vars['enhance_compatibility'])
        enhance_compat_cb.grid(row=8, column=0, padx=10, pady=5, sticky="w")
        
        self.config_vars['disable_rich_text_translate'] = tk.BooleanVar()
        disable_rich_cb = ctk.CTkCheckBox(self.advanced_scroll, text="禁用富文本翻译", variable=self.config_vars['disable_rich_text_translate'])
        disable_rich_cb.grid(row=9, column=0, padx=10, pady=5, sticky="w")
        
        # OCR选项
        ocr_label = ctk.CTkLabel(self.advanced_scroll, text="🔍 OCR选项", font=ctk.CTkFont(size=16, weight="bold"))
        ocr_label.grid(row=10, column=0, padx=10, pady=(20, 5), sticky="w")
        
        self.config_vars['skip_scanned_detection'] = tk.BooleanVar()
        skip_scanned_cb = ctk.CTkCheckBox(
            self.advanced_scroll, 
            text="跳过扫描检测", 
            variable=self.config_vars['skip_scanned_detection']
        )
        skip_scanned_cb.grid(row=11, column=0, padx=10, pady=5, sticky="w")
        
        self.config_vars['ocr_workaround'] = tk.BooleanVar()
        ocr_workaround_cb = ctk.CTkCheckBox(
            self.advanced_scroll, 
            text="启用OCR变通方案", 
            variable=self.config_vars['ocr_workaround']
        )
        ocr_workaround_cb.grid(row=12, column=0, padx=10, pady=5, sticky="w")
        
        self.config_vars['auto_enable_ocr_workaround'] = tk.BooleanVar(value=True)  # 默认开启
        auto_ocr_cb = ctk.CTkCheckBox(
            self.advanced_scroll, 
            text="自动启用OCR（推荐）", 
            variable=self.config_vars['auto_enable_ocr_workaround']
        )
        auto_ocr_cb.grid(row=13, column=0, padx=10, pady=5, sticky="w")
        
        self.config_vars['translate_table_text'] = tk.BooleanVar()
        translate_table_cb = ctk.CTkCheckBox(
            self.advanced_scroll,
            text="翻译表格文本（实验性）",
            variable=self.config_vars['translate_table_text']
        )
        translate_table_cb.grid(row=14, column=0, padx=10, pady=5, sticky="w")

        # 强制分割短行
        self.config_vars['split_short_lines'] = tk.BooleanVar()
        split_short_lines_cb = ctk.CTkCheckBox(
            self.advanced_scroll,
            text="强制分割短行（可能影响排版）",
            variable=self.config_vars['split_short_lines']
        )
        split_short_lines_cb.grid(row=15, column=0, padx=10, pady=5, sticky="w")

        # 双语PDF选项
        dual_label = ctk.CTkLabel(self.advanced_scroll, text="📖 双语PDF选项", font=ctk.CTkFont(size=16, weight="bold"))
        dual_label.grid(row=16, column=0, padx=10, pady=(20, 5), sticky="w")

        self.config_vars['dual_translate_first'] = tk.BooleanVar()
        dual_first_cb = ctk.CTkCheckBox(
            self.advanced_scroll,
            text="双语PDF中翻译页在前",
            variable=self.config_vars['dual_translate_first']
        )
        dual_first_cb.grid(row=17, column=0, padx=10, pady=5, sticky="w")

        self.config_vars['use_alternating_pages_dual'] = tk.BooleanVar()
        alternating_cb = ctk.CTkCheckBox(
            self.advanced_scroll,
            text="使用交替页面模式",
            variable=self.config_vars['use_alternating_pages_dual']
        )
        alternating_cb.grid(row=18, column=0, padx=10, pady=5, sticky="w")

        self.config_vars['only_include_translated_page'] = tk.BooleanVar()
        only_translated_cb = ctk.CTkCheckBox(
            self.advanced_scroll,
            text="仅包含翻译页面（需指定页面范围）",
            variable=self.config_vars['only_include_translated_page']
        )
        only_translated_cb.grid(row=19, column=0, padx=10, pady=5, sticky="w")

        # 新增：智能OCR模式
        self.config_vars['smart_ocr_mode'] = tk.BooleanVar()
        smart_ocr_cb = ctk.CTkCheckBox(
            self.advanced_scroll,
            text="智能OCR模式（解决文本重叠）",
            variable=self.config_vars['smart_ocr_mode']
        )
        smart_ocr_cb.grid(row=20, column=0, padx=10, pady=5, sticky="w")

        # 公式处理选项
        formula_label = ctk.CTkLabel(self.advanced_scroll, text="🧮 公式处理", font=ctk.CTkFont(size=16, weight="bold"))
        formula_label.grid(row=21, column=0, padx=10, pady=(20, 5), sticky="w")

        self.config_vars['add_formula_placehold_hint'] = tk.BooleanVar()
        formula_hint_cb = ctk.CTkCheckBox(
            self.advanced_scroll,
            text="添加公式占位符提示（可能影响翻译质量）",
            variable=self.config_vars['add_formula_placehold_hint']
        )
        formula_hint_cb.grid(row=22, column=0, padx=10, pady=5, sticky="w")

        # 字体选择
        font_label = ctk.CTkLabel(self.advanced_scroll, text="🔤 字体选项", font=ctk.CTkFont(size=16, weight="bold"))
        font_label.grid(row=23, column=0, padx=10, pady=(20, 5), sticky="w")

        font_frame = ctk.CTkFrame(self.advanced_scroll)
        font_frame.grid(row=24, column=0, padx=10, pady=5, sticky="ew")
        font_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(font_frame, text="主要字体族:").grid(row=0, column=0, padx=10, pady=10, sticky="w")
        self.config_vars['primary_font_family'] = tk.StringVar(value="auto")
        font_combo = ctk.CTkComboBox(
            font_frame,
            variable=self.config_vars['primary_font_family'],
            values=["auto", "serif", "sans-serif", "script"]
        )
        font_combo.grid(row=0, column=1, padx=10, pady=10, sticky="ew")

        # OCR说明文本
        ocr_help_text = ctk.CTkLabel(
            self.advanced_scroll,
            text="💡 自动OCR功能会检测扫描页面并自动启用OCR处理\n   表格翻译需要额外安装: pip install rapidocr-onnxruntime\n   智能OCR模式可避免文本重叠问题，推荐与表格翻译同时使用",
            font=ctk.CTkFont(size=11),
            text_color=("gray60", "gray40")
        )
        ocr_help_text.grid(row=17, column=0, padx=20, pady=5, sticky="w")

        # 调试选项
        debug_label = ctk.CTkLabel(self.advanced_scroll, text="🐛 调试选项", font=ctk.CTkFont(size=16, weight="bold"))
        debug_label.grid(row=25, column=0, padx=10, pady=(20, 5), sticky="w")

        self.config_vars['debug'] = tk.BooleanVar()
        debug_cb = ctk.CTkCheckBox(
            self.advanced_scroll,
            text="启用调试模式",
            variable=self.config_vars['debug']
        )
        debug_cb.grid(row=26, column=0, padx=10, pady=5, sticky="w")

        self.config_vars['show_char_box'] = tk.BooleanVar()
        char_box_cb = ctk.CTkCheckBox(
            self.advanced_scroll,
            text="显示字符框（调试用）",
            variable=self.config_vars['show_char_box']
        )
        char_box_cb.grid(row=27, column=0, padx=10, pady=5, sticky="w")

        # 术语表选项
        glossary_label = ctk.CTkLabel(self.advanced_scroll, text="📚 术语表选项", font=ctk.CTkFont(size=16, weight="bold"))
        glossary_label.grid(row=28, column=0, padx=10, pady=(20, 5), sticky="w")

        self.config_vars['save_auto_extracted_glossary'] = tk.BooleanVar()
        save_glossary_cb = ctk.CTkCheckBox(
            self.advanced_scroll,
            text="保存自动提取的术语表",
            variable=self.config_vars['save_auto_extracted_glossary']
        )
        save_glossary_cb.grid(row=29, column=0, padx=10, pady=5, sticky="w")

        # 其他高级选项
        other_label = ctk.CTkLabel(self.advanced_scroll, text="⚡ 其他选项", font=ctk.CTkFont(size=16, weight="bold"))
        other_label.grid(row=30, column=0, padx=10, pady=(20, 5), sticky="w")

        other_frame = ctk.CTkFrame(self.advanced_scroll)
        other_frame.grid(row=31, column=0, padx=10, pady=5, sticky="ew")
        other_frame.grid_columnconfigure(2, weight=1)
        
        # 最小文本长度
        ctk.CTkLabel(other_frame, text="最小文本长度:").grid(row=0, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['min_text_length'] = tk.StringVar(value="5")
        min_text_entry = ctk.CTkEntry(other_frame, textvariable=self.config_vars['min_text_length'], width=100)
        min_text_entry.grid(row=0, column=1, padx=10, pady=5, sticky="w")

        # 分割因子
        ctk.CTkLabel(other_frame, text="分割因子:").grid(row=1, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['short_line_split_factor'] = tk.StringVar(value="0.8")
        split_factor_entry = ctk.CTkEntry(other_frame, textvariable=self.config_vars['short_line_split_factor'], width=100)
        split_factor_entry.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        split_factor_help = ctk.CTkLabel(other_frame, text="(0.1-1.0, 影响短行分割阈值)", font=ctk.CTkFont(size=10))
        split_factor_help.grid(row=1, column=2, padx=(10, 0), pady=5, sticky="w")

        # 最大页面数/部分
        ctk.CTkLabel(other_frame, text="最大页面数/部分:").grid(row=2, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['max_pages_per_part'] = tk.StringVar()
        max_pages_entry = ctk.CTkEntry(other_frame, textvariable=self.config_vars['max_pages_per_part'], width=100, placeholder_text="留空不分割")
        max_pages_entry.grid(row=2, column=1, padx=10, pady=5, sticky="w")

        max_pages_help = ctk.CTkLabel(other_frame, text="(分割翻译时每部分的最大页数)", font=ctk.CTkFont(size=10))
        max_pages_help.grid(row=2, column=2, padx=(10, 0), pady=5, sticky="w")

        # 进度报告间隔
        ctk.CTkLabel(other_frame, text="进度报告间隔(秒):").grid(row=3, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['report_interval'] = tk.StringVar(value="0.1")
        report_entry = ctk.CTkEntry(other_frame, textvariable=self.config_vars['report_interval'], width=100)
        report_entry.grid(row=3, column=1, padx=10, pady=5, sticky="w")

        report_help = ctk.CTkLabel(other_frame, text="(进度更新频率)", font=ctk.CTkFont(size=10))
        report_help.grid(row=3, column=2, padx=(10, 0), pady=5, sticky="w")
        
        # 术语表文件
        ctk.CTkLabel(other_frame, text="术语表文件:").grid(row=2, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['glossary_files'] = tk.StringVar()
        glossary_frame = ctk.CTkFrame(other_frame, fg_color="transparent")
        glossary_frame.grid(row=2, column=1, columnspan=2, padx=10, pady=5, sticky="ew")
        glossary_frame.grid_columnconfigure(0, weight=1)

        glossary_entry = ctk.CTkEntry(glossary_frame, textvariable=self.config_vars['glossary_files'], placeholder_text="选择CSV术语表文件")
        glossary_entry.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        glossary_btn = ctk.CTkButton(glossary_frame, text="浏览", width=60, command=self.select_glossary_files)
        glossary_btn.grid(row=0, column=1, sticky="w")

        # 工作目录
        ctk.CTkLabel(other_frame, text="工作目录:").grid(row=3, column=0, padx=10, pady=5, sticky="w")
        self.config_vars['working_dir'] = tk.StringVar()
        working_dir_frame = ctk.CTkFrame(other_frame, fg_color="transparent")
        working_dir_frame.grid(row=3, column=1, columnspan=2, padx=10, pady=5, sticky="ew")
        working_dir_frame.grid_columnconfigure(0, weight=1)

        working_dir_entry = ctk.CTkEntry(working_dir_frame, textvariable=self.config_vars['working_dir'], placeholder_text="留空使用临时目录")
        working_dir_entry.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        working_dir_btn = ctk.CTkButton(working_dir_frame, text="浏览", width=60, command=self.select_working_dir)
        working_dir_btn.grid(row=0, column=1, sticky="w")

        # 自定义系统提示
        ctk.CTkLabel(other_frame, text="自定义系统提示:").grid(row=5, column=0, padx=10, pady=5, sticky="nw")
        self.config_vars['custom_system_prompt'] = tk.StringVar()
        self.prompt_textbox = ctk.CTkTextbox(other_frame, height=60)
        self.prompt_textbox.grid(row=5, column=1, columnspan=2, padx=10, pady=5, sticky="ew")
        
    def create_progress_tab(self):
        """创建进度标签页"""
        self.progress_frame = ctk.CTkFrame(self.main_frame)
        self.progress_frame.grid_columnconfigure(0, weight=1)
        self.progress_frame.grid_rowconfigure(1, weight=1)
        
        # 标题
        title_label = ctk.CTkLabel(
            self.progress_frame, 
            text="📊 翻译进度", 
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")
        
        # 主内容滚动框架
        main_scroll_frame = ctk.CTkScrollableFrame(self.progress_frame)
        main_scroll_frame.grid(row=1, column=0, padx=20, pady=10, sticky="nsew")
        main_scroll_frame.grid_columnconfigure(0, weight=1)
        
        # 进度信息框架
        progress_info_frame = ctk.CTkFrame(main_scroll_frame)
        progress_info_frame.grid(row=0, column=0, padx=0, pady=(0, 15), sticky="ew")
        progress_info_frame.grid_columnconfigure(0, weight=1)
        
        # 总体进度条
        self.overall_progress_label = ctk.CTkLabel(progress_info_frame, text="总体进度:", font=ctk.CTkFont(size=14, weight="bold"))
        self.overall_progress_label.grid(row=0, column=0, padx=20, pady=(20, 5), sticky="w")
        
        self.overall_progress = ctk.CTkProgressBar(progress_info_frame)
        self.overall_progress.grid(row=1, column=0, padx=20, pady=5, sticky="ew")
        self.overall_progress.set(0)
        
        # 当前阶段进度条
        self.stage_progress_label = ctk.CTkLabel(progress_info_frame, text="当前阶段:", font=ctk.CTkFont(size=14, weight="bold"))
        self.stage_progress_label.grid(row=2, column=0, padx=20, pady=(15, 5), sticky="w")
        
        self.stage_progress = ctk.CTkProgressBar(progress_info_frame)
        self.stage_progress.grid(row=3, column=0, padx=20, pady=5, sticky="ew")
        self.stage_progress.set(0)
        
        # 状态标签
        self.status_label = ctk.CTkLabel(progress_info_frame, text="准备中...", font=ctk.CTkFont(size=12))
        self.status_label.grid(row=4, column=0, padx=20, pady=(5, 20), sticky="w")
        
        # 时间统计框架
        time_stats_frame = ctk.CTkFrame(main_scroll_frame)
        time_stats_frame.grid(row=1, column=0, padx=0, pady=(0, 15), sticky="ew")
        time_stats_frame.grid_columnconfigure(0, weight=1)
        
        # 时间统计标题
        time_title_label = ctk.CTkLabel(
            time_stats_frame, 
            text="⏱️ 时间统计", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        time_title_label.grid(row=0, column=0, padx=20, pady=(15, 10), sticky="w")
        
        # 整体时间显示
        self.overall_time_label = ctk.CTkLabel(
            time_stats_frame, 
            text="总用时: 0s", 
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.overall_time_label.grid(row=1, column=0, padx=20, pady=5, sticky="w")
        
        # 当前阶段时间
        self.current_stage_time_label = ctk.CTkLabel(
            time_stats_frame, 
            text="当前阶段: -", 
            font=ctk.CTkFont(size=12)
        )
        self.current_stage_time_label.grid(row=2, column=0, padx=20, pady=2, sticky="w")
        
        # 阶段时间统计文本框
        stage_time_label = ctk.CTkLabel(
            time_stats_frame, 
            text="各阶段耗时:", 
            font=ctk.CTkFont(size=12, weight="bold")
        )
        stage_time_label.grid(row=3, column=0, padx=20, pady=(10, 5), sticky="w")
        
        self.stage_time_text = ctk.CTkTextbox(time_stats_frame, height=150)
        self.stage_time_text.grid(row=4, column=0, padx=20, pady=(0, 15), sticky="ew")
        self.stage_time_text.insert("0.0", "等待翻译开始...")
        
        # 文件处理统计框架
        file_stats_frame = ctk.CTkFrame(main_scroll_frame)
        file_stats_frame.grid(row=2, column=0, padx=0, pady=0, sticky="ew")
        file_stats_frame.grid_columnconfigure(0, weight=1)
        
        # 文件统计标题
        file_title_label = ctk.CTkLabel(
            file_stats_frame, 
            text="📁 文件处理统计", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        file_title_label.grid(row=0, column=0, padx=20, pady=(15, 10), sticky="w")
        
        # 文件处理进度
        self.file_progress_label = ctk.CTkLabel(
            file_stats_frame, 
            text="文件进度: 0/0", 
            font=ctk.CTkFont(size=12)
        )
        self.file_progress_label.grid(row=1, column=0, padx=20, pady=2, sticky="w")
        
        # 平均处理时间
        self.avg_file_time_label = ctk.CTkLabel(
            file_stats_frame, 
            text="平均文件处理时间: -", 
            font=ctk.CTkFont(size=12)
        )
        self.avg_file_time_label.grid(row=2, column=0, padx=20, pady=2, sticky="w")
        
        # 文件处理详情
        file_detail_label = ctk.CTkLabel(
            file_stats_frame, 
            text="文件处理详情:", 
            font=ctk.CTkFont(size=12, weight="bold")
        )
        file_detail_label.grid(row=3, column=0, padx=20, pady=(10, 5), sticky="w")
        
        self.file_time_text = ctk.CTkTextbox(file_stats_frame, height=120)
        self.file_time_text.grid(row=4, column=0, padx=20, pady=(0, 15), sticky="ew")
        self.file_time_text.insert("0.0", "等待文件处理...")
        
        
    def create_logs_tab(self):
        """创建日志标签页"""
        self.logs_frame = ctk.CTkFrame(self.main_frame)
        self.logs_frame.grid_columnconfigure(0, weight=1)
        self.logs_frame.grid_rowconfigure(1, weight=1)
        
        # 标题
        title_label = ctk.CTkLabel(
            self.logs_frame, 
            text="📋 运行日志", 
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")
        
        # 日志文本框
        self.log_textbox = ctk.CTkTextbox(self.logs_frame)
        self.log_textbox.grid(row=1, column=0, padx=20, pady=10, sticky="nsew")
        
        # 日志控制按钮
        log_buttons_frame = ctk.CTkFrame(self.logs_frame)
        log_buttons_frame.grid(row=2, column=0, padx=20, pady=(0, 20), sticky="ew")
        
        clear_log_btn = ctk.CTkButton(
            log_buttons_frame,
            text="🗑️ 清空日志",
            command=self.clear_log,
            width=120
        )
        clear_log_btn.grid(row=0, column=0, padx=10, pady=10)
        
        # 设置日志处理器
        class GUILogHandler(logging.Handler):
            def __init__(self, text_widget, root):
                super().__init__()
                self.text_widget = text_widget
                self.root = root
                
            def emit(self, record):
                try:
                    msg = self.format(record)
                    # 使用after方法确保在主线程中更新GUI
                    self.root.after(0, self._update_log, msg)
                except:
                    pass
                    
            def _update_log(self, msg):
                try:
                    self.text_widget.insert("end", msg + "\n")
                    self.text_widget.see("end")
                except:
                    pass
        
        self.log_handler = GUILogHandler(self.log_textbox, self.root)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        self.log_handler.setFormatter(formatter)
        
        # 添加到根日志记录器
        logging.getLogger().addHandler(self.log_handler)
        logging.getLogger().setLevel(logging.INFO)
        
    def show_tab(self, tab_name):
        """显示指定的标签页"""
        # 隐藏所有标签页
        for frame in [self.files_frame, self.config_frame, self.advanced_frame, 
                     self.progress_frame, self.logs_frame]:
            frame.grid_remove()
        
        # 重置所有按钮样式
        for key, btn in self.nav_buttons.items():
            btn.configure(fg_color=("gray75", "gray25"))
        
        # 显示选中的标签页并高亮按钮
        if tab_name == "files":
            self.files_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        elif tab_name == "config":
            self.config_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        elif tab_name == "advanced":
            self.advanced_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        elif tab_name == "progress":
            self.progress_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        elif tab_name == "logs":
            self.logs_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        
        # 高亮当前按钮
        if tab_name in self.nav_buttons:
            self.nav_buttons[tab_name].configure(fg_color=("blue", "darkblue"))
        
        self.current_tab = tab_name
        

        
    def add_files(self):
        """添加PDF文件"""
        files = filedialog.askopenfilenames(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        for file in files:
            if file not in self.selected_files:
                self.selected_files.append(file)
        
        self.update_files_display()
        
    def clear_files(self):
        """清空文件列表"""
        self.selected_files.clear()
        self.update_files_display()
        logging.info("文件列表已清空")

    def preview_files(self):
        """预览选中的文件"""
        if not self.selected_files:
            messagebox.showwarning("提示", "请先添加PDF文件")
            return

        # 创建预览窗口
        preview_window = ctk.CTkToplevel(self.root)
        preview_window.title("📋 文件预览")
        preview_window.geometry("600x400")
        preview_window.transient(self.root)

        # 居中显示
        preview_window.update_idletasks()
        x = (preview_window.winfo_screenwidth() // 2) - (600 // 2)
        y = (preview_window.winfo_screenheight() // 2) - (400 // 2)
        preview_window.geometry(f"600x400+{x}+{y}")

        # 预览内容
        ctk.CTkLabel(
            preview_window,
            text="📋 文件列表预览",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        # 文件信息
        info_frame = ctk.CTkScrollableFrame(preview_window)
        info_frame.pack(fill="both", expand=True, padx=20, pady=10)

        for i, file_path in enumerate(self.selected_files, 1):
            file_frame = ctk.CTkFrame(info_frame)
            file_frame.pack(fill="x", pady=5)

            # 文件名
            ctk.CTkLabel(
                file_frame,
                text=f"{i}. {Path(file_path).name}",
                font=ctk.CTkFont(size=14, weight="bold")
            ).pack(anchor="w", padx=10, pady=(10, 5))

            # 文件路径
            ctk.CTkLabel(
                file_frame,
                text=f"路径: {file_path}",
                font=ctk.CTkFont(size=10),
                text_color=("gray60", "gray40")
            ).pack(anchor="w", padx=10, pady=(0, 5))

            # 文件大小
            try:
                size = Path(file_path).stat().st_size
                size_mb = size / (1024 * 1024)
                ctk.CTkLabel(
                    file_frame,
                    text=f"大小: {size_mb:.2f} MB",
                    font=ctk.CTkFont(size=10),
                    text_color=("gray60", "gray40")
                ).pack(anchor="w", padx=10, pady=(0, 10))
            except:
                pass

        # 关闭按钮
        ctk.CTkButton(
            preview_window,
            text="关闭",
            command=preview_window.destroy,
            width=100,
            height=35
        ).pack(pady=20)
        
    def update_files_display(self):
        """更新文件显示"""
        self.files_textbox.delete("1.0", "end")
        for i, file in enumerate(self.selected_files, 1):
            filename = os.path.basename(file)
            self.files_textbox.insert("end", f"{i}. {filename}\n   路径: {file}\n\n")
            
    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir_var.set(directory)
            
    def clear_log(self):
        """清空日志"""
        self.log_textbox.delete("1.0", "end")
        
    def validate_config(self) -> bool:
        """验证配置"""
        if not self.selected_files:
            messagebox.showerror("错误", "请至少选择一个PDF文件")
            return False
            
        if not self.config_vars['api_key'].get().strip():
            messagebox.showerror("错误", "请输入OpenAI API Key")
            return False
            
        try:
            qps = int(self.config_vars['qps'].get())
            if qps <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("错误", "QPS限制必须是正整数")
            return False
            
        try:
            min_text_length = int(self.config_vars['min_text_length'].get())
            if min_text_length < 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("错误", "最小文本长度必须是非负整数")
            return False
            
        return True
        
    def start_translation(self):
        """开始翻译"""
        print("DEBUG: start_translation() 开始执行")
        logging.info("=== 开始翻译流程 ===")
        
        if not self.validate_config():
            print("DEBUG: 配置验证失败")
            logging.error("配置验证失败")
            return
            
        if self.translation_running:
            print("DEBUG: 翻译已在运行")
            messagebox.showwarning("警告", "翻译正在进行中")
            return
            
        print("DEBUG: 配置验证通过")
        logging.info("配置验证通过，准备开始翻译")
        
        # 使用新的线程安全时间统计功能
        from pdfbao.time_tracker import translation_time_tracker
        translation_time_tracker.reset()
        translation_time_tracker.start_overall()
        logging.info("时间统计器已重置并启动")

        # 启动时间统计更新定时器
        self.start_time_update_timer()
        logging.info("时间统计更新定时器已启动")
        
        print("DEBUG: 准备切换按钮状态")
        # 切换按钮状态
        self.start_button.configure(state="disabled")
        self.stop_button.configure(state="normal")
        self.translation_running = True
        print("DEBUG: 按钮状态切换完成")
        
        # 切换到进度标签页
        print("DEBUG: 切换到进度标签页")
        self.show_tab("progress")
        
        # 重置进度条
        print("DEBUG: 重置进度条")
        self.overall_progress.set(0)
        self.stage_progress.set(0)
        self.status_label.configure(text="准备中...")
        
        # 清空日志
        print("DEBUG: 清空日志")
        self.log_textbox.delete("1.0", "end")
        
        logging.info(f"选中文件数量: {len(self.selected_files)}")
        for i, file_path in enumerate(self.selected_files):
            logging.info(f"文件 {i+1}: {file_path}")
        
        # 在新线程中运行翻译
        print("DEBUG: 准备启动翻译线程")
        logging.info("启动翻译线程...")
        self.translation_thread = threading.Thread(target=self.run_translation)
        self.translation_thread.daemon = True
        print("DEBUG: 翻译线程对象创建完成，准备启动")
        self.translation_thread.start()
        print("DEBUG: 翻译线程已启动")
        logging.info("翻译线程已启动")
        
    def stop_translation(self):
        """停止翻译"""
        self.translation_running = False
        self.start_button.configure(state="normal")
        self.stop_button.configure(state="disabled")

        # 停止时间统计更新定时器
        self.stop_time_update_timer()

        logging.info("翻译已停止")
        
    def run_translation(self):
        """运行翻译（在后台线程中）"""
        try:
            print("DEBUG: run_translation() 开始执行")
            print("DEBUG: 准备调用logging.info")
            sys.stdout.flush()  # 强制刷新输出

            # 暂时注释掉可能有问题的logging调用
            # logging.info("翻译线程开始执行")
            print("DEBUG: logging.info调用完成")

            # 创建异步事件循环
            print("DEBUG: 准备创建异步事件循环")
            sys.stdout.flush()  # 强制刷新输出

            # logging.info("创建异步事件循环")
            print("DEBUG: 准备调用asyncio.new_event_loop()")
            loop = asyncio.new_event_loop()
            print("DEBUG: 异步事件循环创建完成")
            asyncio.set_event_loop(loop)
            print("DEBUG: 异步事件循环设置完成")
            
            print("DEBUG: 准备执行异步翻译")
            sys.stdout.flush()  # 强制刷新输出
            # logging.info("开始执行异步翻译")
            print("DEBUG: 准备调用loop.run_until_complete")
            loop.run_until_complete(self.async_translate())
            print("DEBUG: 异步翻译执行完成")
            # logging.info("异步翻译执行完成")
            
        except Exception as e:
            print(f"DEBUG: run_translation() 发生异常: {e}")
            logging.error(f"翻译过程中出错: {str(e)}")
            import traceback
            print(f"DEBUG: 异常详情: {traceback.format_exc()}")
            logging.error(f"错误详情: {traceback.format_exc()}")
        finally:
            print("DEBUG: run_translation() 进入finally块")
            logging.info("翻译线程结束，恢复按钮状态")
            # 恢复按钮状态
            self.root.after(0, self.translation_finished)
            print("DEBUG: run_translation() 执行完成")
            
    async def async_translate(self):
        """异步翻译函数"""
        try:
            print("DEBUG: async_translate() 开始执行")
            logging.info("异步翻译函数开始执行")
            
            # 预热资源
            print("DEBUG: 开始预热资源")
            logging.info("正在初始化资源...")
            
            print("DEBUG: 调用 pdfbao.assets.assets.warmup()")
            pdfbao.assets.assets.warmup()
            print("DEBUG: assets.warmup() 完成")
            logging.info("assets.warmup() 完成")
            
            print("DEBUG: 调用 pdfbao.format.pdf.high_level.init()")
            pdfbao.format.pdf.high_level.init()
            print("DEBUG: high_level.init() 完成")
            logging.info("high_level.init() 完成")
            logging.info("资源初始化阶段完成")
            
            # 创建翻译器
            print("DEBUG: 开始创建翻译器")
            translator = OpenAITranslator(
                lang_in=self.config_vars['lang_in'].get(),
                lang_out=self.config_vars['lang_out'].get(),
                model=self.config_vars['model'].get(),
                base_url=self.config_vars['base_url'].get() or None,
                api_key=self.config_vars['api_key'].get(),
                ignore_cache=self.config_vars['ignore_cache'].get(),
            )
            print("DEBUG: 翻译器创建完成")
            
            # 设置并发参数
            print("DEBUG: 设置并发参数")
            qps = int(self.config_vars['qps'].get())
            pool_max_workers = int(self.config_vars['pool_max_workers'].get())
            set_translate_rate_limiter(qps)
            print("DEBUG: 并发参数设置完成")
            
            logging.info(f"🚀 使用并发配置: QPS={qps}, 线程数={pool_max_workers}")
            
            # 加载文档布局模型
            print("DEBUG: 开始加载文档布局模型")
            doc_layout_model = DocLayoutModel.load_onnx()
            print("DEBUG: 文档布局模型加载完成")
            
            # 处理表格翻译模型
            print("DEBUG: 处理表格翻译模型")
            table_model = None
            if self.config_vars['translate_table_text'].get():
                try:
                    print("DEBUG: 尝试加载表格OCR模型")
                    from pdfbao.docvision.table_detection.rapidocr import RapidOCRModel
                    table_model = RapidOCRModel()
                    print("DEBUG: 表格OCR模型加载成功")
                    logging.info("表格OCR模型加载成功")
                except ImportError:
                    print("DEBUG: 表格OCR模块未安装")
                    logging.warning("表格翻译需要安装 rapidocr-onnxruntime，请运行: pip install rapidocr-onnxruntime")
                    table_model = None
                except Exception as e:
                    print(f"DEBUG: 表格OCR模型加载失败: {e}")
                    logging.warning(f"表格OCR模型加载失败: {e}")
                    table_model = None
            
            # 水印模式映射
            print("DEBUG: 设置水印模式映射")
            watermark_mode_map = {
                "watermarked": WatermarkOutputMode.Watermarked,
                "no_watermark": WatermarkOutputMode.NoWatermark,
                "both": WatermarkOutputMode.Both,
            }
            
            # 处理每个文件
            print("DEBUG: 开始循环处理文件")
            total_files = len(self.selected_files)
            
            for i, file_path in enumerate(self.selected_files):
                if not self.translation_running:
                    print("DEBUG: 翻译被停止，退出循环")
                    break
                    
                print(f"DEBUG: 开始处理文件 {i+1}/{total_files}: {file_path}")
                logging.info(f"开始翻译文件 ({i+1}/{total_files}): {os.path.basename(file_path)}")
                
                # 创建配置
                print("DEBUG: 创建翻译配置")
                config = TranslationConfig(
                    input_file=file_path,
                    pages=self.config_vars['pages'].get() or None,
                    output_dir=self.output_dir_var.get() or None,
                    translator=translator,
                    lang_in=self.config_vars['lang_in'].get(),
                    lang_out=self.config_vars['lang_out'].get(),
                    no_dual=self.config_vars['no_dual'].get(),
                    no_mono=self.config_vars['no_mono'].get(),
                    qps=qps,
                    pool_max_workers=pool_max_workers,
                    skip_clean=self.config_vars['skip_clean'].get(),
                    enhance_compatibility=self.config_vars['enhance_compatibility'].get(),
                    disable_rich_text_translate=self.config_vars['disable_rich_text_translate'].get(),
                    min_text_length=int(self.config_vars['min_text_length'].get()),
                    watermark_output_mode=watermark_mode_map[self.config_vars['watermark_mode'].get()],
                    doc_layout_model=doc_layout_model,
                    custom_system_prompt=self.prompt_textbox.get("1.0", "end-1c").strip() or None,
                    # OCR相关选项
                    skip_scanned_detection=self.config_vars['skip_scanned_detection'].get(),
                    ocr_workaround=self.config_vars['ocr_workaround'].get(),
                    auto_enable_ocr_workaround=self.config_vars['auto_enable_ocr_workaround'].get(),
                    # 表格翻译模型
                    table_model=table_model,
                    # 智能OCR模式
                    smart_ocr_mode=self.config_vars['smart_ocr_mode'].get(),
                    # 短行分割选项
                    split_short_lines=self.config_vars['split_short_lines'].get(),
                    short_line_split_factor=float(self.config_vars['short_line_split_factor'].get()),
                    # 双语PDF选项
                    dual_translate_first=self.config_vars['dual_translate_first'].get(),
                    use_alternating_pages_dual=self.config_vars['use_alternating_pages_dual'].get(),
                    only_include_translated_page=self.config_vars['only_include_translated_page'].get(),
                    # 公式处理
                    add_formula_placehold_hint=self.config_vars['add_formula_placehold_hint'].get(),
                    # 字体选择
                    primary_font_family=self.config_vars['primary_font_family'].get() if self.config_vars['primary_font_family'].get() != "auto" else None,
                    # 调试选项
                    debug=self.config_vars['debug'].get(),
                    show_char_box=self.config_vars['show_char_box'].get(),
                    # 术语表选项
                    glossaries=self._load_glossaries() if self.config_vars['glossary_files'].get() else None,
                    save_auto_extracted_glossary=self.config_vars['save_auto_extracted_glossary'].get(),
                    # 其他选项
                    split_strategy=self._create_split_strategy() if self.config_vars['max_pages_per_part'].get() else None,
                    report_interval=float(self.config_vars['report_interval'].get()),
                    working_dir=self.config_vars['working_dir'].get() if self.config_vars['working_dir'].get() else None,
                )
                print("DEBUG: 翻译配置创建完成")
                
                # 开始翻译
                print("DEBUG: 调用 pdfbao.format.pdf.high_level.async_translate")
                async for event in pdfbao.format.pdf.high_level.async_translate(config):
                    print(f"DEBUG: 收到翻译事件: {event.get('type', 'unknown')}")
                    if not self.translation_running:
                        print("DEBUG: 翻译被停止，退出事件循环")
                        break
                        
                    # 更新进度
                    self.root.after(0, self.update_progress, event, i, total_files)
                    
                    if event["type"] == "error":
                        print(f"DEBUG: 翻译发生错误: {event['error']}")
                        logging.error(f"翻译错误: {event['error']}")
                        break
                    elif event["type"] == "finish":
                        result = event["translate_result"]
                        print(f"DEBUG: 文件翻译完成: {result}")
                        logging.info(f"文件翻译完成: {result}")
                        break
                        
            print("DEBUG: 文件处理循环完成")
            logging.info(f"总计使用Token: {translator.token_count.value}")
            logging.info(f"提示Token: {translator.prompt_token_count.value}")
            logging.info(f"补全Token: {translator.completion_token_count.value}")
            
        except Exception as e:
            print(f"DEBUG: async_translate() 发生异常: {e}")
            logging.error(f"翻译失败: {str(e)}")
            import traceback
            print(f"DEBUG: 异常详情详情: {traceback.format_exc()}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            
    def update_progress(self, event, file_index, total_files):
        """更新进度显示"""
        if event["type"] == "progress_start":
            self.status_label.configure(text=f"文件 {file_index+1}/{total_files} - {event['stage']}")
            
        elif event["type"] == "progress_update":
            # 更新阶段进度
            stage_progress = event['stage_current'] / event['stage_total']
            self.stage_progress.set(stage_progress)
            
            # 更新总体进度
            file_progress = (file_index / total_files + event['overall_progress'] / (100 * total_files))
            self.overall_progress.set(file_progress)
            
            self.status_label.configure(
                text=f"文件 {file_index+1}/{total_files} - {event['stage']} ({event['stage_current']}/{event['stage_total']})"
            )
            
        elif event["type"] == "progress_end":
            self.status_label.configure(text=f"文件 {file_index+1}/{total_files} - {event['stage']} 完成")
    
    def update_time_statistics(self):
        """更新时间统计显示 - 使用新的线程安全API"""
        try:
            stats = translation_time_tracker.get_performance_stats()
            if not stats:
                return

            overall = stats.get('overall', {})
            stages = stats.get('stages', {})
            files = stats.get('files', {})

            # 更新总体时间
            if overall and 'formatted_duration' in overall:
                self.overall_time_label.configure(
                    text=f"总用时: {overall['formatted_duration']}"
                )

            # 更新当前阶段时间
            current_stage = translation_time_tracker.get_current_stage_info()
            if current_stage:
                stage_name, duration = current_stage
                # 简化格式化方法，避免复杂的TimeRecord创建
                if duration < 1:
                    formatted_duration = f"{duration*1000:.1f}ms"
                elif duration < 60:
                    formatted_duration = f"{duration:.2f}s"
                elif duration < 3600:
                    minutes = int(duration // 60)
                    seconds = duration % 60
                    formatted_duration = f"{minutes}m {seconds:.1f}s"
                else:
                    hours = int(duration // 3600)
                    minutes = int((duration % 3600) // 60)
                    seconds = duration % 60
                    formatted_duration = f"{hours}h {minutes}m {seconds:.1f}s"

                self.current_stage_time_label.configure(
                    text=f"当前阶段: {stage_name} ({formatted_duration})"
                )
            else:
                self.current_stage_time_label.configure(text="当前阶段: -")
            
            # 更新阶段统计
            stage_text = "各阶段耗时统计:\n" + "="*40 + "\n"
            if stages:
                for stage_name, info in stages.items():
                    status = " (进行中)" if info['is_running'] else ""
                    percentage = f" ({info.get('percentage', 0):.1f}%)" if 'percentage' in info else ""
                    stage_text += f"{stage_name}: {info['formatted']}{percentage}{status}\n"
            else:
                stage_text += "暂无阶段数据\n"
            
            self.stage_time_text.delete("0.0", "end")
            self.stage_time_text.insert("0.0", stage_text)
            
            # 更新文件统计
            self.file_progress_label.configure(
                text=f"文件进度: {overall['completed_files']}/{overall['total_files']}"
            )
            
            # 更新平均处理时间
            if stats['avg_file_time'] > 0:
                # 创建临时计时器来格式化平均时间，避免访问共享的stage_timers
                from pdfbao.time_tracker import StageTimer
                temp_timer = StageTimer("temp")
                temp_timer.duration = stats['avg_file_time']
                self.avg_file_time_label.configure(
                    text=f"平均文件处理时间: {temp_timer.get_formatted_duration()}"
                )
            else:
                self.avg_file_time_label.configure(text="平均文件处理时间: -")
            
            # 更新文件处理详情
            file_text = "文件处理详情:\n" + "="*30 + "\n"
            if files:
                for file_name, info in sorted(files.items()):
                    status = " (处理中)" if info['is_running'] else " ✓"
                    file_text += f"{file_name}: {info['formatted']}{status}\n"
            else:
                file_text += "暂无文件处理数据\n"
            
            self.file_time_text.delete("0.0", "end")
            self.file_time_text.insert("0.0", file_text)
            
        except Exception as e:
            logging.error(f"更新时间统计显示失败: {e}")
    
    def start_time_update_timer(self):
        """启动时间统计更新定时器"""
        def update_timer():
            while hasattr(self, 'time_update_running') and self.time_update_running:
                try:
                    # 使用root.after确保在主线程中更新UI
                    self.root.after(0, self.update_time_statistics)
                    time.sleep(1)  # 每秒更新一次
                except Exception as e:
                    logging.error(f"时间统计更新定时器错误: {e}")
                    break

        self.time_update_running = True
        self.time_update_thread = threading.Thread(target=update_timer, daemon=True)
        self.time_update_thread.start()
    
    def stop_time_update_timer(self):
        """停止时间统计更新定时器"""
        self.time_update_running = False
            
    def translation_finished(self):
        """翻译完成后的清理工作"""
        print("DEBUG: translation_finished() 开始执行")
        self.translation_running = False
        self.start_button.configure(state="normal")
        self.stop_button.configure(state="disabled")
        self.overall_progress.set(1.0)
        self.stage_progress.set(1.0)
        self.status_label.configure(text="翻译完成!")

        print("DEBUG: 准备停止时间统计更新定时器")
        # 停止时间统计更新定时器
        self.stop_time_update_timer()

        print("DEBUG: 准备更新时间统计显示")
        # 使用新的线程安全时间统计显示更新
        self.update_time_statistics()
        print("DEBUG: translation_finished() 执行完成")
        
    def save_config(self):
        """保存配置到文件"""
        config_data = {}
        for key, var in self.config_vars.items():
            if isinstance(var, tk.BooleanVar):
                config_data[key] = var.get()
            else:
                config_data[key] = var.get()
        
        # 特殊处理自定义系统提示
        config_data['custom_system_prompt'] = self.prompt_textbox.get("1.0", "end-1c")
        config_data['output_dir'] = self.output_dir_var.get()
        
        try:
            config_file = Path("pdf_bao_gui_config.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            messagebox.showinfo("成功", f"配置已保存到 {config_file}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
            
    def select_glossary_files(self):
        """选择术语表文件"""
        files = filedialog.askopenfilenames(
            title="选择术语表文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if files:
            # 多个文件用逗号分隔
            self.config_vars['glossary_files'].set(','.join(files))

    def select_working_dir(self):
        """选择工作目录"""
        directory = filedialog.askdirectory(title="选择工作目录")
        if directory:
            self.config_vars['working_dir'].set(directory)

    def _load_glossaries(self):
        """加载术语表文件"""
        try:
            from pdfbao.glossary import Glossary
            glossary_files = self.config_vars['glossary_files'].get()
            if not glossary_files:
                return None

            glossaries = []
            target_lang = self.config_vars['lang_out'].get()

            for file_path in glossary_files.split(','):
                file_path = file_path.strip()
                if file_path and Path(file_path).exists():
                    try:
                        glossary = Glossary.from_csv(file_path, target_lang)
                        glossaries.append(glossary)
                        logging.info(f"已加载术语表: {file_path}")
                    except Exception as e:
                        logging.warning(f"加载术语表失败 {file_path}: {e}")

            return glossaries if glossaries else None
        except Exception as e:
            logging.error(f"术语表加载错误: {e}")
            return None

    def _create_split_strategy(self):
        """创建分割策略"""
        try:
            from pdfbao.format.pdf.translation_config import TranslationConfig
            max_pages = self.config_vars['max_pages_per_part'].get()
            if max_pages:
                max_pages_int = int(max_pages)
                return TranslationConfig.create_max_pages_per_part_split_strategy(max_pages_int)
            return None
        except Exception as e:
            logging.error(f"创建分割策略错误: {e}")
            return None

    def toggle_theme(self):
        """切换主题"""
        if self.current_theme == "light":
            ctk.set_appearance_mode("dark")
            self.current_theme = "dark"
            self.theme_button.configure(text="☀️ 浅色")
        else:
            ctk.set_appearance_mode("light")
            self.current_theme = "light"
            self.theme_button.configure(text="🌙 深色")

        # 保存主题设置
        self.save_theme_setting()

    def show_settings(self):
        """显示设置对话框"""
        settings_window = ctk.CTkToplevel(self.root)
        settings_window.title("⚙️ PDF-Bao 设置")
        settings_window.geometry("400x300")
        settings_window.transient(self.root)
        settings_window.grab_set()

        # 居中显示
        settings_window.update_idletasks()
        x = (settings_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (settings_window.winfo_screenheight() // 2) - (300 // 2)
        settings_window.geometry(f"400x300+{x}+{y}")

        # 设置内容
        ctk.CTkLabel(settings_window, text="🎨 界面设置", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=20)

        # 主题选择
        theme_frame = ctk.CTkFrame(settings_window)
        theme_frame.pack(pady=10, padx=20, fill="x")

        ctk.CTkLabel(theme_frame, text="主题模式:").pack(side="left", padx=10, pady=10)

        theme_var = tk.StringVar(value=self.current_theme)
        theme_menu = ctk.CTkOptionMenu(
            theme_frame,
            variable=theme_var,
            values=["light", "dark", "system"],
            command=self.change_theme
        )
        theme_menu.pack(side="right", padx=10, pady=10)

        # 关闭按钮
        ctk.CTkButton(
            settings_window,
            text="关闭",
            command=settings_window.destroy
        ).pack(pady=20)

    def change_theme(self, new_theme):
        """改变主题"""
        ctk.set_appearance_mode(new_theme)
        self.current_theme = new_theme
        if new_theme == "dark":
            self.theme_button.configure(text="☀️ 浅色")
        else:
            self.theme_button.configure(text="🌙 深色")
        self.save_theme_setting()

    def save_theme_setting(self):
        """保存主题设置"""
        try:
            theme_file = Path("pdf_bao_theme.json")
            with open(theme_file, 'w', encoding='utf-8') as f:
                json.dump({"theme": self.current_theme}, f)
        except Exception as e:
            logging.warning(f"保存主题设置失败: {e}")

    def load_theme_setting(self):
        """加载主题设置"""
        try:
            theme_file = Path("pdf_bao_theme.json")
            if theme_file.exists():
                with open(theme_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    theme = data.get("theme", "light")
                    self.change_theme(theme)
        except Exception as e:
            logging.warning(f"加载主题设置失败: {e}")

    def load_config(self):
        """从文件加载配置"""
        try:
            config_file = Path("pdf_bao_gui_config.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                for key, value in config_data.items():
                    if key == 'output_dir':
                        self.output_dir_var.set(value)
                    elif key == 'custom_system_prompt':
                        self.prompt_textbox.delete("1.0", "end")
                        self.prompt_textbox.insert("1.0", value)
                    elif key in self.config_vars:
                        self.config_vars[key].set(value)

                logging.info(f"配置已从 {config_file} 加载")
        except Exception as e:
            logging.warning(f"加载配置失败: {str(e)}")

        # 加载主题设置
        self.load_theme_setting()
            
    def run(self):
        """运行GUI应用"""
        self.root.mainloop()


def main():
    """主函数"""
    app = ModernPDFBaoGUI()
    app.run()


if __name__ == "__main__":
    main()