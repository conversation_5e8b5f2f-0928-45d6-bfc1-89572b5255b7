<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="32" height="32" rx="6" fill="url(#gradient1)"/>
  
  <!-- PDF文档图标 -->
  <g transform="translate(6, 4)">
    <!-- 文档主体 -->
    <rect x="0" y="2" width="16" height="20" rx="1.5" fill="white" stroke="#E5E7EB" stroke-width="0.8"/>
    
    <!-- 文档折角 -->
    <path d="M12 2 L16 2 L16 6 L12 6 Z" fill="#F3F4F6"/>
    <path d="M12 2 L12 6 L16 6" stroke="#D1D5DB" stroke-width="0.8" fill="none"/>
    
    <!-- PDF文字 -->
    <text x="8" y="10" font-family="Arial, sans-serif" font-size="4" font-weight="bold" text-anchor="middle" fill="#DC2626">PDF</text>
    
    <!-- 翻译箭头 -->
    <path d="M2 16 L14 16 M12 14 L14 16 L12 18" stroke="#3B82F6" stroke-width="1.5" fill="none" stroke-linecap="round"/>
  </g>
  
  <!-- 熊猫图标 -->
  <circle cx="26" cy="8" r="4" fill="#10B981"/>
  <text x="26" y="10" font-family="Arial, sans-serif" font-size="3" font-weight="bold" text-anchor="middle" fill="white">🐼</text>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.9"/>
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1"/>
    </linearGradient>
  </defs>
</svg>
