"""
pdfbao 异常处理模块
提供分层的异常处理体系和错误恢复机制
"""

from .base_exceptions import (
    pdfbaoError,
    pdfbaoWarning,
    RecoverableError,
    CriticalError
)

from .translation_exceptions import (
    TranslationError,
    TranslatorInitError,
    TranslationTimeoutError,
    TranslationRateLimitError,
    TranslationAPIError,
    TranslationCacheError
)

from .pdf_exceptions import (
    PDFProcessingError,
    PDFParsingError,
    PDFCorruptedError,
    PDFPermissionError,
    PDFLayoutError,
    PDFFontError,
    PDFTextExtractionError
)

from .config_exceptions import (
    ConfigurationError,
    ConfigValidationError,
    ConfigLoadError,
    ConfigSaveError
)

from .network_exceptions import (
    NetworkError,
    ConnectionError,
    TimeoutError,
    RateLimitError,
    AuthenticationError
)

from .error_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ErrorContext,
    RetryStrategy,
    BackoffStrategy
)

from .error_reporter import (
    ErrorReporter,
    ErrorReport,
    ErrorSeverity
)

__all__ = [
    # Base exceptions
    'pdfbaoError',
    'pdfbaoWarning', 
    'RecoverableError',
    'CriticalError',
    
    # Translation exceptions
    'TranslationError',
    'TranslatorInitError',
    'TranslationTimeoutError',
    'TranslationRateLimitError',
    'TranslationAPIError',
    'TranslationCacheError',
    
    # PDF exceptions
    'PDFProcessingError',
    'PDFParsingError',
    'PDFCorruptedError',
    'PDFPermissionError',
    'PDFLayoutError',
    'PDFFontError',
    'PDFTextExtractionError',
    
    # Config exceptions
    'ConfigurationError',
    'ConfigValidationError',
    'ConfigLoadError',
    'ConfigSaveError',
    
    # Network exceptions
    'NetworkError',
    'ConnectionError',
    'TimeoutError',
    'RateLimitError',
    'AuthenticationError',
    
    # Error handling
    'ErrorHandler',
    'ErrorContext',
    'RetryStrategy',
    'BackoffStrategy',
    
    # Error reporting
    'ErrorReporter',
    'ErrorReport',
    'ErrorSeverity'
]
