"""
基础异常类
定义 pdfbao 的异常层次结构
"""

import traceback
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class pdfbaoError(Exception):
    """pdfbao 基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
        suggestions: Optional[List[str]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.severity = severity
        self.context = context or {}
        self.cause = cause
        self.suggestions = suggestions or []
        self.timestamp = datetime.now()
        self.traceback_str = traceback.format_exc() if cause else None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_type': self.__class__.__name__,
            'error_code': self.error_code,
            'message': self.message,
            'severity': self.severity.value,
            'timestamp': self.timestamp.isoformat(),
            'context': self.context,
            'suggestions': self.suggestions,
            'traceback': self.traceback_str
        }
    
    def add_context(self, key: str, value: Any) -> 'pdfbaoError':
        """添加上下文信息"""
        self.context[key] = value
        return self
    
    def add_suggestion(self, suggestion: str) -> 'pdfbaoError':
        """添加解决建议"""
        self.suggestions.append(suggestion)
        return self
    
    def __str__(self) -> str:
        """字符串表示"""
        parts = [f"[{self.error_code}] {self.message}"]
        
        if self.context:
            context_str = ", ".join(f"{k}={v}" for k, v in self.context.items())
            parts.append(f"Context: {context_str}")
        
        if self.suggestions:
            suggestions_str = "; ".join(self.suggestions)
            parts.append(f"Suggestions: {suggestions_str}")
        
        return " | ".join(parts)


class pdfbaoWarning(UserWarning):
    """pdfbao 警告类"""
    
    def __init__(
        self,
        message: str,
        category: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.category = category or "General"
        self.context = context or {}
        self.timestamp = datetime.now()


class RecoverableError(pdfbaoError):
    """可恢复的错误"""
    
    def __init__(
        self,
        message: str,
        recovery_action: Optional[str] = None,
        max_retries: int = 3,
        **kwargs
    ):
        super().__init__(message, severity=ErrorSeverity.MEDIUM, **kwargs)
        self.recovery_action = recovery_action
        self.max_retries = max_retries
        self.retry_count = 0
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.retry_count < self.max_retries
    
    def increment_retry(self):
        """增加重试计数"""
        self.retry_count += 1


class CriticalError(pdfbaoError):
    """严重错误，需要立即停止处理"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, severity=ErrorSeverity.CRITICAL, **kwargs)


class ValidationError(pdfbaoError):
    """验证错误"""
    
    def __init__(
        self,
        field: str,
        message: str,
        value: Any = None,
        **kwargs
    ):
        super().__init__(
            f"Validation failed for '{field}': {message}",
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )
        self.field = field
        self.value = value
        self.add_context('field', field)
        if value is not None:
            self.add_context('value', str(value))


class ResourceError(pdfbaoError):
    """资源相关错误"""
    
    def __init__(
        self,
        resource_type: str,
        resource_id: str,
        message: str,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.resource_type = resource_type
        self.resource_id = resource_id
        self.add_context('resource_type', resource_type)
        self.add_context('resource_id', resource_id)


class PermissionError(pdfbaoError):
    """权限错误"""
    
    def __init__(
        self,
        operation: str,
        resource: str,
        message: Optional[str] = None,
        **kwargs
    ):
        message = message or f"Permission denied for operation '{operation}' on '{resource}'"
        super().__init__(message, severity=ErrorSeverity.HIGH, **kwargs)
        self.operation = operation
        self.resource = resource
        self.add_context('operation', operation)
        self.add_context('resource', resource)


class TimeoutError(RecoverableError):
    """超时错误"""
    
    def __init__(
        self,
        operation: str,
        timeout_seconds: float,
        message: Optional[str] = None,
        **kwargs
    ):
        message = message or f"Operation '{operation}' timed out after {timeout_seconds} seconds"
        super().__init__(
            message,
            recovery_action="Retry with longer timeout",
            **kwargs
        )
        self.operation = operation
        self.timeout_seconds = timeout_seconds
        self.add_context('operation', operation)
        self.add_context('timeout_seconds', timeout_seconds)


class DependencyError(CriticalError):
    """依赖错误"""
    
    def __init__(
        self,
        dependency: str,
        message: Optional[str] = None,
        **kwargs
    ):
        message = message or f"Missing or invalid dependency: {dependency}"
        super().__init__(message, **kwargs)
        self.dependency = dependency
        self.add_context('dependency', dependency)
        self.add_suggestion(f"Install or update dependency: {dependency}")


class CompatibilityError(pdfbaoError):
    """兼容性错误"""
    
    def __init__(
        self,
        component: str,
        required_version: str,
        current_version: str,
        message: Optional[str] = None,
        **kwargs
    ):
        message = message or (
            f"Incompatible {component} version. "
            f"Required: {required_version}, Current: {current_version}"
        )
        super().__init__(message, severity=ErrorSeverity.HIGH, **kwargs)
        self.component = component
        self.required_version = required_version
        self.current_version = current_version
        self.add_context('component', component)
        self.add_context('required_version', required_version)
        self.add_context('current_version', current_version)
