adobe_standard = [
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    "space",
    "exclam",
    "quotedbl",
    "numbersign",
    "dollar",
    "percent",
    "ampersand",
    "quoteright",
    "parenleft",
    "parenright",
    "asterisk",
    "plus",
    "comma",
    "hyphen",
    "period",
    "slash",
    "zero",
    "one",
    "two",
    "three",
    "four",
    "five",
    "six",
    "seven",
    "eight",
    "nine",
    "colon",
    "semicolon",
    "less",
    "equal",
    "greater",
    "question",
    "at",
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
    "bracketleft",
    "backslash",
    "bracketright",
    "asciicircum",
    "underscore",
    "quoteleft",
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z",
    "braceleft",
    "bar",
    "braceright",
    "asciitilde",
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    "exclamdown",
    "cent",
    "sterling",
    "fraction",
    "yen",
    "florin",
    "section",
    "currency",
    "quotesingle",
    "quotedblleft",
    "guillemotleft",
    "guilsinglleft",
    "guilsinglright",
    "fi",
    "fl",
    None,
    "endash",
    "dagger",
    "daggerdbl",
    "periodcentered",
    None,
    "paragraph",
    "bullet",
    "quotesinglbase",
    "quotedblbase",
    "quotedblright",
    "guillemotright",
    "ellipsis",
    "perthousand",
    None,
    "questiondown",
    None,
    "grave",
    "acute",
    "circumflex",
    "tilde",
    "macron",
    "breve",
    "dotaccent",
    "dieresis",
    None,
    "ring",
    "cedilla",
    None,
    "hungarumlaut",
    "ogonek",
    "caron",
    "emdash",
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    "AE",
    None,
    "ordfeminine",
    None,
    None,
    None,
    None,
    "Lslash",
    "Oslash",
    "OE",
    "ordmasculine",
    None,
    None,
    None,
    None,
    None,
    "ae",
    None,
    None,
    None,
    "dotlessi",
    None,
    None,
    "lslash",
    "oslash",
    "oe",
    "germandbls",
    None,
    None,
    None,
    None,
]

mac_expert = [
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    "space",
    "exclamsmall",
    "Hungarumlautsmall",
    "centoldstyle",
    "dollaroldstyle",
    "dollarsuperior",
    "ampersandsmall",
    "Acutesmall",
    "parenleftsuperior",
    "parenrightsuperior",
    "twodotenleader",
    "onedotenleader",
    "comma",
    "hyphen",
    "period",
    "fraction",
    "zerooldstyle",
    "oneoldstyle",
    "twooldstyle",
    "threeoldstyle",
    "fouroldstyle",
    "fiveoldstyle",
    "sixoldstyle",
    "sevenoldstyle",
    "eightoldstyle",
    "nineoldstyle",
    "colon",
    "semicolon",
    None,
    "threequartersemdash",
    None,
    "questionsmall",
    None,
    None,
    None,
    None,
    "Ethsmall",
    None,
    None,
    "onequarter",
    "onehalf",
    "threequarters",
    "oneeighth",
    "threeeighths",
    "fiveeighths",
    "seveneighths",
    "onethird",
    "twothirds",
    None,
    None,
    None,
    None,
    None,
    None,
    "ff",
    "fi",
    "fl",
    "ffi",
    "ffl",
    "parenleftinferior",
    None,
    "parenrightinferior",
    "Circumflexsmall",
    "hypheninferior",
    "Gravesmall",
    "Asmall",
    "Bsmall",
    "Csmall",
    "Dsmall",
    "Esmall",
    "Fsmall",
    "Gsmall",
    "Hsmall",
    "Ismall",
    "Jsmall",
    "Ksmall",
    "Lsmall",
    "Msmall",
    "Nsmall",
    "Osmall",
    "Psmall",
    "Qsmall",
    "Rsmall",
    "Ssmall",
    "Tsmall",
    "Usmall",
    "Vsmall",
    "Wsmall",
    "Xsmall",
    "Ysmall",
    "Zsmall",
    "colonmonetary",
    "onefitted",
    "rupiah",
    "Tildesmall",
    None,
    None,
    "asuperior",
    "centsuperior",
    None,
    None,
    None,
    None,
    "Aacutesmall",
    "Agravesmall",
    "Acircumflexsmall",
    "Adieresissmall",
    "Atildesmall",
    "Aringsmall",
    "Ccedillasmall",
    "Eacutesmall",
    "Egravesmall",
    "Ecircumflexsmall",
    "Edieresissmall",
    "Iacutesmall",
    "Igravesmall",
    "Icircumflexsmall",
    "Idieresissmall",
    "Ntildesmall",
    "Oacutesmall",
    "Ogravesmall",
    "Ocircumflexsmall",
    "Odieresissmall",
    "Otildesmall",
    "Uacutesmall",
    "Ugravesmall",
    "Ucircumflexsmall",
    "Udieresissmall",
    None,
    "eightsuperior",
    "fourinferior",
    "threeinferior",
    "sixinferior",
    "eightinferior",
    "seveninferior",
    "Scaronsmall",
    None,
    "centinferior",
    "twoinferior",
    None,
    "Dieresissmall",
    None,
    "Caronsmall",
    "osuperior",
    "fiveinferior",
    None,
    "commainferior",
    "periodinferior",
    "Yacutesmall",
    None,
    "dollarinferior",
    None,
    None,
    "Thornsmall",
    None,
    "nineinferior",
    "zeroinferior",
    "Zcaronsmall",
    "AEsmall",
    "Oslashsmall",
    "questiondownsmall",
    "oneinferior",
    "Lslashsmall",
    None,
    None,
    None,
    None,
    None,
    None,
    "Cedillasmall",
    None,
    None,
    None,
    None,
    None,
    "OEsmall",
    "figuredash",
    "hyphensuperior",
    None,
    None,
    None,
    None,
    "exclamdownsmall",
    None,
    "Ydieresissmall",
    None,
    "onesuperior",
    "twosuperior",
    "threesuperior",
    "foursuperior",
    "fivesuperior",
    "sixsuperior",
    "sevensuperior",
    "ninesuperior",
    "zerosuperior",
    None,
    "esuperior",
    "rsuperior",
    "tsuperior",
    None,
    None,
    "isuperior",
    "ssuperior",
    "dsuperior",
    None,
    None,
    None,
    None,
    None,
    "lsuperior",
    "Ogoneksmall",
    "Brevesmall",
    "Macronsmall",
    "bsuperior",
    "nsuperior",
    "msuperior",
    "commasuperior",
    "periodsuperior",
    "Dotaccentsmall",
    "Ringsmall",
    None,
    None,
    None,
    None,
]

mac_roman = [
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    "space",
    "exclamsmall",
    "Hungarumlautsmall",
    "centoldstyle",
    "dollaroldstyle",
    "dollarsuperior",
    "ampersandsmall",
    "Acutesmall",
    "parenleftsuperior",
    "parenrightsuperior",
    "twodotenleader",
    "onedotenleader",
    "comma",
    "hyphen",
    "period",
    "fraction",
    "zerooldstyle",
    "oneoldstyle",
    "twooldstyle",
    "threeoldstyle",
    "fouroldstyle",
    "fiveoldstyle",
    "sixoldstyle",
    "sevenoldstyle",
    "eightoldstyle",
    "nineoldstyle",
    "colon",
    "semicolon",
    None,
    "threequartersemdash",
    None,
    "questionsmall",
    None,
    None,
    None,
    None,
    "Ethsmall",
    None,
    None,
    "onequarter",
    "onehalf",
    "threequarters",
    "oneeighth",
    "threeeighths",
    "fiveeighths",
    "seveneighths",
    "onethird",
    "twothirds",
    None,
    None,
    None,
    None,
    None,
    None,
    "ff",
    "fi",
    "fl",
    "ffi",
    "ffl",
    "parenleftinferior",
    None,
    "parenrightinferior",
    "Circumflexsmall",
    "hypheninferior",
    "Gravesmall",
    "Asmall",
    "Bsmall",
    "Csmall",
    "Dsmall",
    "Esmall",
    "Fsmall",
    "Gsmall",
    "Hsmall",
    "Ismall",
    "Jsmall",
    "Ksmall",
    "Lsmall",
    "Msmall",
    "Nsmall",
    "Osmall",
    "Psmall",
    "Qsmall",
    "Rsmall",
    "Ssmall",
    "Tsmall",
    "Usmall",
    "Vsmall",
    "Wsmall",
    "Xsmall",
    "Ysmall",
    "Zsmall",
    "colonmonetary",
    "onefitted",
    "rupiah",
    "Tildesmall",
    None,
    None,
    "asuperior",
    "centsuperior",
    None,
    None,
    None,
    None,
    "Aacutesmall",
    "Agravesmall",
    "Acircumflexsmall",
    "Adieresissmall",
    "Atildesmall",
    "Aringsmall",
    "Ccedillasmall",
    "Eacutesmall",
    "Egravesmall",
    "Ecircumflexsmall",
    "Edieresissmall",
    "Iacutesmall",
    "Igravesmall",
    "Icircumflexsmall",
    "Idieresissmall",
    "Ntildesmall",
    "Oacutesmall",
    "Ogravesmall",
    "Ocircumflexsmall",
    "Odieresissmall",
    "Otildesmall",
    "Uacutesmall",
    "Ugravesmall",
    "Ucircumflexsmall",
    "Udieresissmall",
    None,
    "eightsuperior",
    "fourinferior",
    "threeinferior",
    "sixinferior",
    "eightinferior",
    "seveninferior",
    "Scaronsmall",
    None,
    "centinferior",
    "twoinferior",
    None,
    "Dieresissmall",
    None,
    "Caronsmall",
    "osuperior",
    "fiveinferior",
    None,
    "commainferior",
    "periodinferior",
    "Yacutesmall",
    None,
    "dollarinferior",
    None,
    None,
    "Thornsmall",
    None,
    "nineinferior",
    "zeroinferior",
    "Zcaronsmall",
    "AEsmall",
    "Oslashsmall",
    "questiondownsmall",
    "oneinferior",
    "Lslashsmall",
    None,
    None,
    None,
    None,
    None,
    None,
    "Cedillasmall",
    None,
    None,
    None,
    None,
    None,
    "OEsmall",
    "figuredash",
    "hyphensuperior",
    None,
    None,
    None,
    None,
    "exclamdownsmall",
    None,
    "Ydieresissmall",
    None,
    "onesuperior",
    "twosuperior",
    "threesuperior",
    "foursuperior",
    "fivesuperior",
    "sixsuperior",
    "sevensuperior",
    "ninesuperior",
    "zerosuperior",
    None,
    "esuperior",
    "rsuperior",
    "tsuperior",
    None,
    None,
    "isuperior",
    "ssuperior",
    "dsuperior",
    None,
    None,
    None,
    None,
    None,
    "lsuperior",
    "Ogoneksmall",
    "Brevesmall",
    "Macronsmall",
    "bsuperior",
    "nsuperior",
    "msuperior",
    "commasuperior",
    "periodsuperior",
    "Dotaccentsmall",
    "Ringsmall",
    None,
    None,
    None,
    None,
]

win_ansi = [
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    "space",
    "exclam",
    "quotedbl",
    "numbersign",
    "dollar",
    "percent",
    "ampersand",
    "quotesingle",
    "parenleft",
    "parenright",
    "asterisk",
    "plus",
    "comma",
    "hyphen",
    "period",
    "slash",
    "zero",
    "one",
    "two",
    "three",
    "four",
    "five",
    "six",
    "seven",
    "eight",
    "nine",
    "colon",
    "semicolon",
    "less",
    "equal",
    "greater",
    "question",
    "at",
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
    "bracketleft",
    "backslash",
    "bracketright",
    "asciicircum",
    "underscore",
    "grave",
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z",
    "braceleft",
    "bar",
    "braceright",
    "asciitilde",
    "bullet",
    "Euro",
    "bullet",
    "quotesinglbase",
    "florin",
    "quotedblbase",
    "ellipsis",
    "dagger",
    "daggerdbl",
    "circumflex",
    "perthousand",
    "Scaron",
    "guilsinglleft",
    "OE",
    "bullet",
    "Zcaron",
    "bullet",
    "bullet",
    "quoteleft",
    "quoteright",
    "quotedblleft",
    "quotedblright",
    "bullet",
    "endash",
    "emdash",
    "tilde",
    "trademark",
    "scaron",
    "guilsinglright",
    "oe",
    "bullet",
    "zcaron",
    "Ydieresis",
    "space",
    "exclamdown",
    "cent",
    "sterling",
    "currency",
    "yen",
    "brokenbar",
    "section",
    "dieresis",
    "copyright",
    "ordfeminine",
    "guillemotleft",
    "logicalnot",
    "hyphen",
    "registered",
    "macron",
    "degree",
    "plusminus",
    "twosuperior",
    "threesuperior",
    "acute",
    "mu",
    "paragraph",
    "periodcentered",
    "cedilla",
    "onesuperior",
    "ordmasculine",
    "guillemotright",
    "onequarter",
    "onehalf",
    "threequarters",
    "questiondown",
    "Agrave",
    "Aacute",
    "Acircumflex",
    "Atilde",
    "Adieresis",
    "Aring",
    "AE",
    "Ccedilla",
    "Egrave",
    "Eacute",
    "Ecircumflex",
    "Edieresis",
    "Igrave",
    "Iacute",
    "Icircumflex",
    "Idieresis",
    "Eth",
    "Ntilde",
    "Ograve",
    "Oacute",
    "Ocircumflex",
    "Otilde",
    "Odieresis",
    "multiply",
    "Oslash",
    "Ugrave",
    "Uacute",
    "Ucircumflex",
    "Udieresis",
    "Yacute",
    "Thorn",
    "germandbls",
    "agrave",
    "aacute",
    "acircumflex",
    "atilde",
    "adieresis",
    "aring",
    "ae",
    "ccedilla",
    "egrave",
    "eacute",
    "ecircumflex",
    "edieresis",
    "igrave",
    "iacute",
    "icircumflex",
    "idieresis",
    "eth",
    "ntilde",
    "ograve",
    "oacute",
    "ocircumflex",
    "otilde",
    "odieresis",
    "divide",
    "oslash",
    "ugrave",
    "uacute",
    "ucircumflex",
    "udieresis",
    "yacute",
    "thorn",
    "ydieresis",
]


def get_type1_encoding(name):
    match name:
        case "StandardEncoding":
            return adobe_standard
        case "MacRomanEncoding":
            return mac_roman
        case "WinAnsiEncoding":
            return win_ansi
        case "MacExpertEncoding":
            return mac_expert
