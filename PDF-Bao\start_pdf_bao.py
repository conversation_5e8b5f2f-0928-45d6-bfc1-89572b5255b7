#!/usr/bin/env python3
"""
PDF-Bao 主启动器
智能PDF翻译工具
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("🐼 欢迎使用 PDF-Bao")
    print("=" * 50)
    print("🌟 智能PDF翻译工具")
    print("📄 支持多种PDF格式")
    print("🌍 多语言翻译支持")
    print("🎨 现代化用户界面")
    print("=" * 50)
    
    # 检查依赖
    try:
        import customtkinter as ctk
        from PIL import Image
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装: pip install customtkinter pillow")
        return
    
    # 启动GUI
    try:
        print("🚀 启动PDF-Bao图形界面...")
        from pdfbao_gui_modern import ModernPDFBaoGUI

        app = ModernPDFBaoGUI()
        print("✅ 界面创建成功")
        print("🎉 PDF-Bao已启动！")
        
        app.run()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
