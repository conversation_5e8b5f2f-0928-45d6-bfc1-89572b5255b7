<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="url(#gradient1)" stroke="url(#gradient2)" stroke-width="2"/>
  
  <!-- PDF文档图标 -->
  <g transform="translate(16, 12)">
    <!-- 文档主体 -->
    <rect x="2" y="4" width="24" height="32" rx="2" fill="white" stroke="#E5E7EB" stroke-width="1"/>
    
    <!-- 文档折角 -->
    <path d="M20 4 L26 4 L26 10 L20 10 Z" fill="#F3F4F6"/>
    <path d="M20 4 L20 10 L26 10" stroke="#D1D5DB" stroke-width="1" fill="none"/>
    
    <!-- PDF文字 -->
    <text x="13" y="18" font-family="Arial, sans-serif" font-size="6" font-weight="bold" text-anchor="middle" fill="#DC2626">PDF</text>
    
    <!-- 文档内容线条 -->
    <line x1="6" y1="22" x2="22" y2="22" stroke="#9CA3AF" stroke-width="1"/>
    <line x1="6" y1="25" x2="18" y2="25" stroke="#9CA3AF" stroke-width="1"/>
    <line x1="6" y1="28" x2="20" y2="28" stroke="#9CA3AF" stroke-width="1"/>
    <line x1="6" y1="31" x2="16" y2="31" stroke="#9CA3AF" stroke-width="1"/>
  </g>
  
  <!-- 翻译箭头 -->
  <g transform="translate(28, 38)">
    <!-- 箭头主体 -->
    <path d="M0 0 L8 0 L6 -2 M8 0 L6 2" stroke="#3B82F6" stroke-width="2" fill="none" stroke-linecap="round"/>
    <!-- 熊猫符号 (Bao) -->
    <circle cx="4" cy="-6" r="2" fill="#10B981" stroke="white" stroke-width="1"/>
    <text x="4" y="-4.5" font-family="Arial, sans-serif" font-size="3" font-weight="bold" text-anchor="middle" fill="white">🐼</text>
  </g>
  
  <!-- 地球图标 (表示多语言) -->
  <g transform="translate(8, 45)">
    <circle cx="0" cy="0" r="6" fill="#3B82F6" stroke="white" stroke-width="1"/>
    <!-- 地球经纬线 -->
    <ellipse cx="0" cy="0" rx="6" ry="3" fill="none" stroke="white" stroke-width="0.8"/>
    <ellipse cx="0" cy="0" rx="3" ry="6" fill="none" stroke="white" stroke-width="0.8"/>
    <line x1="-6" y1="0" x2="6" y2="0" stroke="white" stroke-width="0.8"/>
  </g>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:0.2"/>
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1"/>
    </linearGradient>
  </defs>
</svg>
