"""
配置验证器
提供配置验证和规范化功能
"""

import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """配置验证错误"""
    
    def __init__(self, field: str, message: str, value: Any = None):
        self.field = field
        self.message = message
        self.value = value
        super().__init__(f"Validation error for '{field}': {message}")


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.errors: List[ValidationError] = []
    
    def validate(self, config: Dict[str, Any]) -> Tuple[bool, List[ValidationError]]:
        """验证配置"""
        self.errors.clear()
        
        # 验证 API 配置
        if 'api' in config:
            self._validate_api_config(config['api'])
        
        # 验证性能配置
        if 'performance' in config:
            self._validate_performance_config(config['performance'])
        
        # 验证翻译配置
        if 'translation' in config:
            self._validate_translation_config(config['translation'])
        
        # 验证输出配置
        if 'output' in config:
            self._validate_output_config(config['output'])
        
        # 验证 UI 配置
        if 'ui' in config:
            self._validate_ui_config(config['ui'])
        
        return len(self.errors) == 0, self.errors.copy()
    
    def _validate_api_config(self, api_config: Dict[str, Any]):
        """验证 API 配置"""
        # 验证 base_url
        if 'base_url' in api_config:
            base_url = api_config['base_url']
            if base_url and not self._is_valid_url(base_url):
                self.errors.append(ValidationError(
                    'api.base_url', 
                    'Invalid URL format', 
                    base_url
                ))
        
        # 验证 api_key
        if 'api_key' in api_config:
            api_key = api_config['api_key']
            if api_key and len(api_key.strip()) < 10:
                self.errors.append(ValidationError(
                    'api.api_key', 
                    'API key too short (minimum 10 characters)', 
                    api_key
                ))
        
        # 验证 timeout
        if 'timeout' in api_config:
            timeout = api_config['timeout']
            if not isinstance(timeout, int) or timeout <= 0:
                self.errors.append(ValidationError(
                    'api.timeout', 
                    'Timeout must be a positive integer', 
                    timeout
                ))
        
        # 验证 max_retries
        if 'max_retries' in api_config:
            max_retries = api_config['max_retries']
            if not isinstance(max_retries, int) or max_retries < 0:
                self.errors.append(ValidationError(
                    'api.max_retries', 
                    'Max retries must be a non-negative integer', 
                    max_retries
                ))
    
    def _validate_performance_config(self, perf_config: Dict[str, Any]):
        """验证性能配置"""
        # 验证 qps
        if 'qps' in perf_config:
            qps = perf_config['qps']
            if not isinstance(qps, int) or qps <= 0:
                self.errors.append(ValidationError(
                    'performance.qps', 
                    'QPS must be a positive integer', 
                    qps
                ))
            elif qps > 100:
                logger.warning(f"High QPS value ({qps}) may cause rate limiting")
        
        # 验证 pool_max_workers
        if 'pool_max_workers' in perf_config:
            workers = perf_config['pool_max_workers']
            if workers is not None:
                if not isinstance(workers, int) or workers <= 0:
                    self.errors.append(ValidationError(
                        'performance.pool_max_workers', 
                        'Pool max workers must be a positive integer', 
                        workers
                    ))
                elif workers > 50:
                    logger.warning(f"High worker count ({workers}) may cause resource issues")
        
        # 验证 max_memory_mb
        if 'max_memory_mb' in perf_config:
            memory = perf_config['max_memory_mb']
            if not isinstance(memory, int) or memory <= 0:
                self.errors.append(ValidationError(
                    'performance.max_memory_mb', 
                    'Max memory must be a positive integer', 
                    memory
                ))
            elif memory < 256:
                logger.warning(f"Low memory limit ({memory}MB) may cause issues")
    
    def _validate_translation_config(self, trans_config: Dict[str, Any]):
        """验证翻译配置"""
        # 验证语言代码
        valid_languages = {
            'en', 'zh', 'zh-cn', 'zh-tw', 'ja', 'ko', 'fr', 'de', 'es', 'it', 'pt', 'ru'
        }
        
        for lang_key in ['lang_in', 'lang_out']:
            if lang_key in trans_config:
                lang = trans_config[lang_key]
                if lang and lang.lower() not in valid_languages:
                    logger.warning(f"Uncommon language code: {lang}")
        
        # 验证页面范围
        if 'pages' in trans_config:
            pages = trans_config['pages']
            if pages and not self._is_valid_page_range(pages):
                self.errors.append(ValidationError(
                    'translation.pages', 
                    'Invalid page range format', 
                    pages
                ))
        
        # 验证最小文本长度
        if 'min_text_length' in trans_config:
            min_length = trans_config['min_text_length']
            if not isinstance(min_length, int) or min_length < 0:
                self.errors.append(ValidationError(
                    'translation.min_text_length',
                    'Min text length must be a non-negative integer',
                    min_length
                ))

        # 验证分割因子
        if 'short_line_split_factor' in trans_config:
            split_factor = trans_config['short_line_split_factor']
            if not isinstance(split_factor, (int, float)) or split_factor <= 0 or split_factor > 1:
                self.errors.append(ValidationError(
                    'translation.short_line_split_factor',
                    'Short line split factor must be a number between 0 and 1',
                    split_factor
                ))
    
    def _validate_output_config(self, output_config: Dict[str, Any]):
        """验证输出配置"""
        # 验证输出目录
        if 'output_dir' in output_config:
            output_dir = output_config['output_dir']
            if output_dir:
                path = Path(output_dir)
                if path.exists() and not path.is_dir():
                    self.errors.append(ValidationError(
                        'output.output_dir', 
                        'Output path exists but is not a directory', 
                        output_dir
                    ))
                elif not path.exists():
                    try:
                        path.mkdir(parents=True, exist_ok=True)
                    except OSError as e:
                        self.errors.append(ValidationError(
                            'output.output_dir', 
                            f'Cannot create output directory: {e}', 
                            output_dir
                        ))
        
        # 验证水印模式
        if 'watermark_mode' in output_config:
            mode = output_config['watermark_mode']
            valid_modes = ['watermarked', 'no_watermark', 'both']
            if mode not in valid_modes:
                self.errors.append(ValidationError(
                    'output.watermark_mode', 
                    f'Invalid watermark mode. Must be one of: {valid_modes}', 
                    mode
                ))
    
    def _validate_ui_config(self, ui_config: Dict[str, Any]):
        """验证 UI 配置"""
        # 验证窗口尺寸
        for dimension in ['window_width', 'window_height']:
            if dimension in ui_config:
                value = ui_config[dimension]
                if not isinstance(value, int) or value < 100:
                    self.errors.append(ValidationError(
                        f'ui.{dimension}', 
                        'Window dimension must be at least 100 pixels', 
                        value
                    ))
        
        # 验证主题
        if 'theme' in ui_config:
            theme = ui_config['theme']
            valid_themes = ['light', 'dark', 'system']
            if theme not in valid_themes:
                self.errors.append(ValidationError(
                    'ui.theme', 
                    f'Invalid theme. Must be one of: {valid_themes}', 
                    theme
                ))
    
    def _is_valid_url(self, url: str) -> bool:
        """验证 URL 格式"""
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return url_pattern.match(url) is not None
    
    def _is_valid_page_range(self, pages: str) -> bool:
        """验证页面范围格式"""
        # 支持格式: "1,2,3", "1-5", "1,3-5,7", "-3", "5-"
        page_pattern = re.compile(r'^(\d+(-\d+)?|(-\d+)|(\d+-))?(,(\d+(-\d+)?|(-\d+)|(\d+-)))*$')
        return page_pattern.match(pages.replace(' ', '')) is not None
