"""
错误处理器
提供统一的错误处理、重试和恢复机制
"""

import asyncio
import logging
import time
import random
from typing import Dict, Any, Optional, Callable, Type, List, Union
from enum import Enum
from dataclasses import dataclass, field
from functools import wraps

from .base_exceptions import (
    pdfbaoError, 
    RecoverableError, 
    CriticalError,
    ErrorSeverity
)

logger = logging.getLogger(__name__)


class BackoffStrategy(Enum):
    """退避策略"""
    FIXED = "fixed"
    LINEAR = "linear"
    EXPONENTIAL = "exponential"
    RANDOM = "random"


@dataclass
class RetryStrategy:
    """重试策略配置"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_strategy: BackoffStrategy = BackoffStrategy.EXPONENTIAL
    backoff_multiplier: float = 2.0
    jitter: bool = True
    retry_on: List[Type[Exception]] = field(default_factory=lambda: [RecoverableError])
    stop_on: List[Type[Exception]] = field(default_factory=lambda: [CriticalError])


@dataclass
class ErrorContext:
    """错误上下文"""
    operation: str
    component: str
    user_data: Dict[str, Any] = field(default_factory=dict)
    system_data: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    
    def add_data(self, key: str, value: Any, is_user_data: bool = True):
        """添加上下文数据"""
        if is_user_data:
            self.user_data[key] = value
        else:
            self.system_data[key] = value


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.retry_strategies: Dict[Type[Exception], RetryStrategy] = {}
        self.error_callbacks: Dict[Type[Exception], List[Callable]] = {}
        self.global_error_callback: Optional[Callable] = None
        self.error_stats: Dict[str, int] = {}
    
    def register_retry_strategy(
        self, 
        exception_type: Type[Exception], 
        strategy: RetryStrategy
    ):
        """注册重试策略"""
        self.retry_strategies[exception_type] = strategy
        logger.debug(f"Registered retry strategy for {exception_type.__name__}")
    
    def register_error_callback(
        self, 
        exception_type: Type[Exception], 
        callback: Callable
    ):
        """注册错误回调"""
        if exception_type not in self.error_callbacks:
            self.error_callbacks[exception_type] = []
        self.error_callbacks[exception_type].append(callback)
        logger.debug(f"Registered error callback for {exception_type.__name__}")
    
    def set_global_error_callback(self, callback: Callable):
        """设置全局错误回调"""
        self.global_error_callback = callback
    
    def handle_error(
        self, 
        error: Exception, 
        context: ErrorContext,
        retry_func: Optional[Callable] = None
    ) -> Any:
        """处理错误"""
        error_type = type(error)
        error_name = error_type.__name__
        
        # 更新错误统计
        self.error_stats[error_name] = self.error_stats.get(error_name, 0) + 1
        
        # 记录错误
        self._log_error(error, context)
        
        # 调用错误回调
        self._call_error_callbacks(error, context)
        
        # 检查是否为严重错误
        if isinstance(error, CriticalError):
            logger.critical(f"Critical error in {context.operation}: {error}")
            raise error
        
        # 尝试恢复
        if isinstance(error, RecoverableError) and retry_func:
            return self._attempt_recovery(error, context, retry_func)
        
        # 如果无法恢复，重新抛出异常
        raise error
    
    def _attempt_recovery(
        self, 
        error: RecoverableError, 
        context: ErrorContext,
        retry_func: Callable
    ) -> Any:
        """尝试错误恢复"""
        error_type = type(error)
        strategy = self._get_retry_strategy(error_type)
        
        if not strategy:
            logger.warning(f"No retry strategy for {error_type.__name__}")
            raise error
        
        # 检查是否应该停止重试
        for stop_type in strategy.stop_on:
            if isinstance(error, stop_type):
                logger.info(f"Stopping retry due to {stop_type.__name__}")
                raise error
        
        # 检查重试次数
        if error.retry_count >= strategy.max_attempts:
            logger.error(f"Max retry attempts ({strategy.max_attempts}) exceeded")
            raise error
        
        # 计算延迟时间
        delay = self._calculate_delay(strategy, error.retry_count)
        
        logger.info(
            f"Retrying {context.operation} in {delay:.2f}s "
            f"(attempt {error.retry_count + 1}/{strategy.max_attempts})"
        )
        
        # 等待
        time.sleep(delay)
        
        # 增加重试计数
        error.increment_retry()
        
        try:
            # 重试操作
            return retry_func()
        except Exception as retry_error:
            # 如果重试失败，递归处理
            return self.handle_error(retry_error, context, retry_func)
    
    def _get_retry_strategy(self, error_type: Type[Exception]) -> Optional[RetryStrategy]:
        """获取重试策略"""
        # 精确匹配
        if error_type in self.retry_strategies:
            return self.retry_strategies[error_type]
        
        # 父类匹配
        for registered_type, strategy in self.retry_strategies.items():
            if issubclass(error_type, registered_type):
                return strategy
        
        # 默认策略
        if issubclass(error_type, RecoverableError):
            return RetryStrategy()
        
        return None
    
    def _calculate_delay(self, strategy: RetryStrategy, attempt: int) -> float:
        """计算延迟时间"""
        if strategy.backoff_strategy == BackoffStrategy.FIXED:
            delay = strategy.base_delay
        elif strategy.backoff_strategy == BackoffStrategy.LINEAR:
            delay = strategy.base_delay * (attempt + 1)
        elif strategy.backoff_strategy == BackoffStrategy.EXPONENTIAL:
            delay = strategy.base_delay * (strategy.backoff_multiplier ** attempt)
        elif strategy.backoff_strategy == BackoffStrategy.RANDOM:
            delay = random.uniform(strategy.base_delay, strategy.max_delay)
        else:
            delay = strategy.base_delay
        
        # 限制最大延迟
        delay = min(delay, strategy.max_delay)
        
        # 添加抖动
        if strategy.jitter:
            jitter_range = delay * 0.1
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    def _log_error(self, error: Exception, context: ErrorContext):
        """记录错误"""
        severity_map = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }
        
        if isinstance(error, pdfbaoError):
            level = severity_map.get(error.severity, logging.ERROR)
        else:
            level = logging.ERROR
        
        logger.log(
            level,
            f"Error in {context.operation} ({context.component}): {error}",
            extra={
                'error_type': type(error).__name__,
                'context': context.user_data,
                'system_context': context.system_data
            }
        )
    
    def _call_error_callbacks(self, error: Exception, context: ErrorContext):
        """调用错误回调"""
        error_type = type(error)
        
        # 调用特定类型的回调
        for registered_type, callbacks in self.error_callbacks.items():
            if isinstance(error, registered_type):
                for callback in callbacks:
                    try:
                        callback(error, context)
                    except Exception as callback_error:
                        logger.error(f"Error in callback: {callback_error}")
        
        # 调用全局回调
        if self.global_error_callback:
            try:
                self.global_error_callback(error, context)
            except Exception as callback_error:
                logger.error(f"Error in global callback: {callback_error}")
    
    def get_error_stats(self) -> Dict[str, int]:
        """获取错误统计"""
        return self.error_stats.copy()
    
    def reset_error_stats(self):
        """重置错误统计"""
        self.error_stats.clear()


# 全局错误处理器实例
_error_handler = ErrorHandler()


def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器"""
    return _error_handler


def with_error_handling(
    operation: str,
    component: str = "unknown",
    retry_strategy: Optional[RetryStrategy] = None
):
    """错误处理装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            context = ErrorContext(operation=operation, component=component)
            
            def retry_func():
                return func(*args, **kwargs)
            
            try:
                return func(*args, **kwargs)
            except Exception as error:
                return _error_handler.handle_error(error, context, retry_func)
        
        return wrapper
    return decorator


def with_async_error_handling(
    operation: str,
    component: str = "unknown",
    retry_strategy: Optional[RetryStrategy] = None
):
    """异步错误处理装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            context = ErrorContext(operation=operation, component=component)
            
            async def retry_func():
                return await func(*args, **kwargs)
            
            try:
                return await func(*args, **kwargs)
            except Exception as error:
                # 对于异步函数，需要特殊处理
                if isinstance(error, RecoverableError):
                    return await _async_attempt_recovery(error, context, retry_func)
                else:
                    return _error_handler.handle_error(error, context)
        
        return wrapper
    return decorator


async def _async_attempt_recovery(
    error: RecoverableError,
    context: ErrorContext,
    retry_func: Callable
) -> Any:
    """异步错误恢复"""
    error_type = type(error)
    strategy = _error_handler._get_retry_strategy(error_type)
    
    if not strategy or error.retry_count >= strategy.max_attempts:
        raise error
    
    delay = _error_handler._calculate_delay(strategy, error.retry_count)
    
    logger.info(
        f"Retrying {context.operation} in {delay:.2f}s "
        f"(attempt {error.retry_count + 1}/{strategy.max_attempts})"
    )
    
    await asyncio.sleep(delay)
    error.increment_retry()
    
    try:
        return await retry_func()
    except Exception as retry_error:
        if isinstance(retry_error, RecoverableError):
            return await _async_attempt_recovery(retry_error, context, retry_func)
        else:
            raise retry_error
